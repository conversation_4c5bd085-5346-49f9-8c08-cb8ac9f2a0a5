import { http } from "@/utils/http";

/** 查询学段列表 */
export const dataStageList = params => {
  return http.request(
    "get",
    "/platform/dataStage/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 查询所有学科
export const subjectListObj = params => {
  return http.request(
    "get",
    "/platform/subject/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 教材版本接口
export const TextbookList = params => {
  return http.request(
    "get",
    "/platform/dataTextbookVersion/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

//   查询年级列表
export const gradeList = params => {
  return http.request(
    "get",
    "/platform/dataGrade/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 查询数据目录
export const dataCatalogList = params => {
  return http.request(
    "get",
    "/platform/dataCatalog/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 知识点相关接口
export const knowledgePointList = params => {
  return http.request(
    "get",
    "/platform/dataKnowledge/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
