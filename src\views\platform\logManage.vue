<script setup>
import { ref, onMounted, nextTick } from "vue";
import { formatTime } from "@/utils/index";
import { useRouter, useRoute } from "vue-router";
import { operateLogFindAll, operateLogSave } from "@/api/institution.js";
const router = useRouter();
const route = useRoute();
onMounted(() => {
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 350px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去表单高度、页面其他元素高度和边距
    tableHeight.value = `calc(100vh - 240px - ${searchFormHeight.value}px)`;
  }
};

// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  phone: "",
  operateLogType: [],
  detail: ""
});

const citiesType = {
  机构管理: "ORGANIZATIONAL_MANAGEMENT",
  实践点管理: "COMPLEX_MANAGEMENT",
  课程管理: "COURSE_MANAGEMENT",
  分类管理: "COURSE_CLASSIFY_MANAGEMENT",
  订单管理: "ORDER_MANAGEMENT",
  财务管理: "FINANCE_MANAGEMENT",
  领队管理: "LEADER_MANAGEMENT",
  讲师管理: "LECTURER_MANAGEMENT",
  学生管理: "STUDENT_MANAGEMENT",
  账号管理: "ACCOUNT_MANAGEMENT",
  权限管理: "AUTHORITY_MANAGEMENT",
  平台设置: "PLATFORM_SETTINGS",
  家长管理: "PARENT_MANAGEMENT",
  师资管理: "TEACHER_DATABASE",
  评价管理: "COMMENTS",
  优惠券管理: "COUPON_MANAGEMENT"
};

const cities = [
  "机构管理",
  "实践点管理",
  "课程管理",
  "分类管理",
  "订单管理",
  "学生管理",
  "领队管理",
  "讲师管理",
  "账号管理",
  "权限管理",
  "平台设置",
  "家长管理",
  "师资管理",
  "评价管理",
  "优惠券管理"
];

const checkAll = ref(true);
const isIndeterminate = ref(false);
const checkedCities = ref([...cities]); //默认选中

function getKeyByValue(value) {
  for (const [key, val] of Object.entries(citiesType)) {
    if (val === value) {
      return key;
    }
  }
  return null; // 如果没有找到对应的值，返回 null
}
const handleCheckAllChange = val => {
  checkedCities.value = val ? cities : [];
  citiesData(checkedCities.value);
  // console.log('🎁-----checkedCities.value-----', checkedCities.value);
  isIndeterminate.value = false;
};
const handleCheckedCitiesChange = value => {
  console.log("🐬-----value-----", value);
  const checkedCount = value.length;
  checkAll.value = checkedCount === cities.length;
  isIndeterminate.value = checkedCount > 0 && checkedCount < cities.length;
  citiesData(value);
};

const citiesData = value => {
  form.value.operateLogType = [];
  for (let index = 0; index < value.length; index++) {
    const element = value[index];
    citiesType[element];
    form.value.operateLogType.push(citiesType[element]);
  }
  form.value.operateLogType = form.value.operateLogType.join();
  console.log("🐬-----isIndeterminate.value-----", form.value.operateLogType);
};
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = paramsData[paramsDataKey] =
          paramsDataKey === "phone"
            ? encryption(String(form.value[paramsDataKey]))
            : form.value[paramsDataKey];
      }
    }
  }
  console.log("🍧-----paramsData-----", paramsData);
  // return;
  try {
    const { code, data, msg } = await operateLogFindAll(paramsData);
    console.log("🎁-----data-----", code, data, msg);
    if (code == 200) {
      tableData.value = data?.content;
      params.value.totalElements = data?.totalElements;
      await nextTick();
      calculateTableHeight();
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
  getListLoading.value = false;
};
const eye_phone = async () => {
  let paramsData = {
    operateLogType: "COMPLEX_MANAGEMENT",
    detail: "冻结"
    // additionalParameter: ''
  };
  try {
    const { code, data, msg } = await operateLogSave(paramsData);
    console.log("🎁-----data-----", code, data, msg);
    if (code == 200) {
      tableData.value = data?.content;
      params.value.totalElements = data?.totalElements;
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};

//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 选择时间
const timeChange = async value => {
  if (!value || value.length !== 2) return;

  // 开始时间（默认 00:00:00）
  form.value.startTime = new Date(value[0]).getTime();

  // 结束时间（设置为 23:59:59.999）
  const endDate = new Date(value[1]);
  endDate.setHours(23, 59, 59, 999); // 关键修改
  form.value.endTime = endDate.getTime();
  console.log("🎁-----endDate.getTime()-----", endDate.getTime());
  await nextTick();
  calculateTableHeight();
};
const clearEvt = () => {
  form.value.startTime = "";
  form.value.endTime = "";
  form.value.name = "";
  form.value.phone = "";
  form.value.operateLogType = [];
};
const value1 = ref([]);
// 重置
const setData = () => {
  form.value = {
    startTime: "",
    endTime: "",
    name: "",
    phone: "",
    operateLogType: [],
    detail: ""
  };
  form.value.roleId = 2;
  params.value.page = 1;
  value1.value = [];
  checkAll.value = true;
  isIndeterminate.value = false;
  getTableList();
  checkedCities.value = [...cities];
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
const defaultTime = [
  new Date(2024, 4, 1, 0, 0, 0),
  new Date(2024, 4, 1, 23, 59, 59)
];
</script>

<template>
  <div class="page-container">
    <div class="common">
      <!-- <div class="con_top">
        <div class="titles">日志管理</div>
        <el-button type="primary">创建课程</el-button>
      </div> -->
      <div class="con_search">
        <el-form :model="form" :inline="true">
          <el-form-item label="操作时间">
            <el-date-picker
              v-model="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              :default-time="defaultTime"
              @change="timeChange"
              @clear="clearEvt"
            />
          </el-form-item>
          <el-form-item label="操作人">
            <el-input v-model.trim="form.name" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="操作描述">
            <el-input
              v-model.trim="form.detail"
              placeholder="请输入操作描述"
              clearable
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item label="操作类型">
            <el-checkbox
              v-model="checkAll"
              :indeterminate="isIndeterminate"
              @change="handleCheckAllChange"
            >
              全部
            </el-checkbox>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <el-checkbox-group
              v-model="checkedCities"
              @change="handleCheckedCitiesChange"
            >
              <el-checkbox
                v-for="city in cities"
                :key="city"
                :label="city"
                :value="city"
              >
                {{ city }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="containers">
      <el-scrollbar :style="{ height: tableHeight }">
        <div class="con_table">
          <el-table
            :data="tableData"
            table-layout="fixed"
            :header-cell-style="{
              backgroundColor: '#fafafa',
              color: '#565353'
            }"
            highlight-current-row
            :max-height="tableHeight"
            :row-style="{ height: '70px' }"
            align="center"
          >
            <el-table-column width="160px" prop="id" label="编号">
              <template #default="scope">
                <el-text>
                  {{ scope.row.id || "--" }}
                </el-text>
              </template>
            </el-table-column>
            <el-table-column width="240px" prop="createdAt" label="操作时间">
              <template #default="scope">
                <div>
                  {{
                    formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm") || "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              width="220px"
              prop="operateLogType"
              label="操作类型"
              align="left"
            >
              <template #default="scope">
                <div>
                  {{ getKeyByValue(scope.row.operateLogType) || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              width="200px"
              prop="username"
              label="操作人员"
              align="left"
            >
              <template #default="scope">
                <div>
                  {{ scope.row.username || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              width="500px"
              prop="detail"
              label="操作描述"
              align="left"
            >
              <template #default="scope">
                <div>
                  {{ scope.row.detail || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="ip" label="操作IP" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.ip || "--" }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-scrollbar>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  // height: v-bind(h);
  overflow: hidden;
}

.scrollbar {
  background-color: #fff;
}

.common {
  padding: 20px 20px 2px 20px;
  background-color: #fff;
  margin-bottom: 20px;
  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;
    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }
  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
  }
}

.containers {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 20px 20px 2px;
  background: #fff;
  overflow: hidden;

  .con_table {
    .button {
      display: flex;
      justify-content: space-around;
      .btnse {
        color: #409eff;
        cursor: pointer;
        padding: 0 5px;
      }
    }
  }
  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin: 16px 0px;
    background-color: #fff;
  }
}
</style>
