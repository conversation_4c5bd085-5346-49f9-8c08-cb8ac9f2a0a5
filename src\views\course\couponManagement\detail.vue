<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { Hide, View } from "@element-plus/icons-vue";
import { couponFindById, findReceiveRecordByCouponId, organizationFindAll, updateCouponOrganization } from "@/api/coupon";
import { requestTo } from "@/utils/http/tool";
import { formatTime } from "@/utils/index";
import { decrypt, encryption } from "@/utils/SM4.js";
import { isEmpty } from "@iceywu/utils";
defineOptions({
  name: "CouponManagementDetail"
});

const router = useRouter();
const route = useRoute();

const isInvalid = computed(() => route.query.valid === "invalid");
const isEditMode = computed(() => route.query.type === "edit");

// 优惠券详情数据
const couponDetail = ref({});

const formatCouponType = type => {
  const types = {
    FULL_REDUCTION: "满减券",
    DISCOUNT: "折扣券",
    FIXED: "立减券"
  };
  return types[type] || "--";
};

const formatDiscountRule = ({
  couponDiscountType,
  discountAmount,
  conditionAmount
}) => {
  if (couponDiscountType === "FULL_REDUCTION") {
    return `满${conditionAmount || 0}减${discountAmount || 0}`;
  } else if (couponDiscountType === "DISCOUNT") {
    return `${discountAmount || 0}折`;
  } else if (couponDiscountType === "FIXED") {
    return `立减${discountAmount || 0}`;
  }
  return "--";
};

const formatTimeRange = (start, end) => {
  const startTime = start ? formatTime(start, "YYYY-MM-DD") : "";
  const endTime = end ? formatTime(end, "YYYY-MM-DD") : "";
  return startTime && endTime ? `${startTime} 至 ${endTime}` : "无限制";
};

// 加载优惠券详情
const loadCouponDetail = async id => {
  if (!id) return;
  const [err, result] = await requestTo(couponFindById({ id }));
  if (err) {
    ElMessage.error("获取优惠券详情失败");
    return;
  }
  if (result) {
    couponDetail.value = {
      couponName: result.name,
      couponType: formatCouponType(result.couponDiscountType),
      status: result.enabled ? "启用" : "停用",
      discountRule: formatDiscountRule(result),
      createTime: result.createdAt ? formatTime(result.createdAt) : "--",
      issueTime: formatTimeRange(
        result.distributionStartTime,
        result.distributionEndTime
      ),
      useTime: formatTimeRange(result.startTime, result.endTime),
      remarks: result.remarks || "--"
    };
    statistics.value = {
      issued: result.totalIssue || 0,
      received: result.receivedNumber || 0,
      used: result.usedNumber || 0
    };

    // 编辑模式下初始化机构选中状态
    if (isEditMode.value && result.organizations && result.organizations.length > 0) {
      selectedOrganizations.value = [...result.organizations];
      selectedOrganizationIds.value = result.organizations.map(item => item.id);
      console.log('初始化选中机构:', selectedOrganizations.value);
      console.log('初始化选中机构ID:', selectedOrganizationIds.value);
    }
  }
};

const detailConfig = computed(() => {
  const config = [
    {
      label1: "优惠券名称",
      prop1: "couponName",
      label2: "优惠券类型",
      prop2: "couponType"
    },
    {
      label1: "状态",
      prop1: "status",
      label2: "优惠门槛与金额",
      prop2: "discountRule"
    },
    {
      label1: "创建时间",
      prop1: "createTime",
      label2: "发放时间",
      prop2: "issueTime"
    },
    {
      label1: "备注",
      prop1: "remarks",
      label2: "使用时间",
      prop2: "useTime"
    }
  ];
  if (isInvalid.value) {
    return config.filter(item => item.prop1 !== "status");
  }
  return config;
});

// 搜索表单数据
const searchForm = reactive({
  userAccount: "",
  couponSource: "",
  couponState: "all"
});

const formatCouponSource = source => {
  const sources = {
    MANUAL: "手动派发",
    ONLINE: "在线领取"
  };
  return sources[source] || "--";
};

const formatFeeType = type => {
  const types = {
    CLASS_HOUR: "课时费",
    INSURANCE: "保险费",
    MATERIAL: "材料费",
    SERVICE: "服务费"
  };
  return types[type] || "--";
};

// 手机号切换显示逻辑
const togglePhoneDisplay = row => {
  row.isPhoneVisible = !row.isPhoneVisible;
};

// 表格列配置
const columns = computed(() => {
  const baseColumns = [
    {
      label: "用户账号",
      prop: "phone",
      minWidth: 120,
      slot: "phone"
    },
    {
      label: "昵称",
      prop: "name",
      minWidth: 100
    },
    {
      label: "领取时间",
      prop: "receiveTime",
      minWidth: 150,
      formatter: ({ receiveTime }) =>
        receiveTime ? formatTime(receiveTime) : "--"
    },
    {
      label: "使用时间",
      prop: "useTime",
      minWidth: 150,
      formatter: ({ useTime }) => (useTime ? formatTime(useTime) : "--")
    },
    {
      label: "优惠费用类型",
      prop: "feeType",
      minWidth: 120,
      formatter: ({ feeType }) => formatFeeType(feeType)
    },
    {
      label: "优惠课期",
      prop: "coursePeriodName",
      minWidth: 150,
      formatter: ({ coursePeriodName }) =>
        coursePeriodName ? coursePeriodName : "--"
    },
    {
      label: "获取方式",
      prop: "couponSource",
      minWidth: 100,
      formatter: ({ couponSource }) => formatCouponSource(couponSource)
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ];
  if (isInvalid.value) {
    return baseColumns.filter(item => item.prop !== "feeType");
  }
  return baseColumns;
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 统计数据
const statistics = ref({
  issued: 0,
  received: 0,
  used: 0
});

// 编辑模式相关数据
const organizationList = ref([]);
const organizationPagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  background: true
});
const organizationLoading = ref(false);
const organizationSearchName = ref("");
const organizationSearchAlias = ref("");
const organizationTableRef = ref();

// 机构选择状态持久化管理
const selectedOrganizations = ref([]);
const selectedOrganizationIds = ref([]);
const isUpdatingSelection = ref(false);

// 编辑模式搜索表单
const editSearchForm = reactive({
  name: "",
  alias: ""
});

// 编辑模式表格列配置
const editColumns = [
  {
    type: "selection",
    width: 55,
    align: "center"
  },
  {
    label: "机构名称",
    prop: "name",
    minWidth: 200,
    formatter: ({ name }) => name || "--"
  },
  {
    label: "机构别名",
    prop: "alias",
    minWidth: 150,
    formatter: ({ alias }) => alias || "--"
  },
  {
    label: "机构ID",
    prop: "id",
    width: 100,
    align: "center"
  }
];

// 加载表格数据
const loadTableData = async () => {
  loading.value = true;
  const couponId = route.query.id;
  if (!couponId) {
    loading.value = false;
    return;
  }

  const params = {
    couponId,
    page: pagination.currentPage - 1,
    size: pagination.pageSize,
    sort: "createdAt,desc",
    ...(searchForm.userAccount && {
      phone: encryption(searchForm.userAccount)
    }),
    ...(searchForm.couponSource && { couponSource: searchForm.couponSource })
    // ...(searchForm.couponState && { couponState: searchForm.couponState })
  };
  if (!isEmpty(searchForm.couponState) && searchForm.couponState !== "all") {
    params.couponState = searchForm.couponState;
  } else {
    delete params.couponState;
  }
  try {
    const [err, result] = await requestTo(findReceiveRecordByCouponId(params));
    if (err) {
      ElMessage.error("获取领取记录失败");
      tableData.value = [];
      pagination.total = 0;
      return;
    }
    if (result) {
      // 为每行数据添加手机号显示状态，默认显示脱敏格式
      tableData.value = (result.content || []).map(item => ({
        ...item,
        isPhoneVisible: false
      }));
      pagination.total = result.totalElements || 0;
    }
  } catch (error) {
    console.error("加载领取记录失败:", error);
    ElMessage.error("获取领取记录失败");
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.currentPage = 1;
  loadTableData();
};

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = "";
  });
  searchForm.couponState = "all";
  pagination.currentPage = 1;
  loadTableData();
};

// 新增优惠券
const handleAddCoupon = () => {
  router.push({
    path: "/coupon/management/createCoupon",
    query: {
      type: "detailCreate"
    }
  });
};

// 关联订单操作
const handleRelateOrder = row => {
  router.push({
    path: "/coupon/management/orderDetail",
    query: {
      id: row.orderId
    }
  });
};

// 返回
const handleBack = () => {
  router.go(-1);
};

// 编辑模式方法
// 获取机构列表
const getOrganizationList = async (resetPage = false) => {
  if (resetPage) {
    organizationPagination.value.currentPage = 1;
  }

  organizationLoading.value = true;
  try {
    const params = {
      page: organizationPagination.value.currentPage - 1,
      size: organizationPagination.value.pageSize,
      freeze: false
    };

    if (organizationSearchName.value.trim()) {
      params.name = organizationSearchName.value.trim();
    }
    if (organizationSearchAlias.value.trim()) {
      params.alias = organizationSearchAlias.value.trim();
    }

    const [err, result] = await requestTo(organizationFindAll(params));
    if (result && result.content) {
      organizationList.value = result.content;
      organizationPagination.value.total = result.totalElements || 0;
      setOrganizationTableSelection();
    }
  } catch (error) {
    console.error("获取机构列表失败:", error);
  } finally {
    organizationLoading.value = false;
  }
};

// 处理机构表格选择变化
const handleOrganizationSelectionChange = (selection) => {
  if (isUpdatingSelection.value) {
    return;
  }

  const currentPageIds = organizationList.value.map(item => item.id);
  const otherPagesSelected = selectedOrganizations.value.filter(
    item => !currentPageIds.includes(item.id)
  );

  selectedOrganizations.value = [...otherPagesSelected, ...selection];
  selectedOrganizationIds.value = selectedOrganizations.value.map(item => item.id);

  console.log('选中机构数量:', selectedOrganizations.value.length);
  console.log('选中机构ID:', selectedOrganizationIds.value);
};

// 设置机构表格的选中状态
const setOrganizationTableSelection = () => {
  if (!organizationTableRef.value || selectedOrganizationIds.value.length === 0) {
    return;
  }

  isUpdatingSelection.value = true;

  setTimeout(() => {
    try {
      const selectedRows = organizationList.value.filter(item =>
        selectedOrganizationIds.value.includes(item.id)
      );

      const tableInstance = organizationTableRef.value.getTableRef();
      if (tableInstance) {
        tableInstance.clearSelection();
        selectedRows.forEach(row => {
          tableInstance.toggleRowSelection(row, true);
        });
      }
    } catch (error) {
      console.error('设置表格选中状态失败:', error);
    } finally {
      isUpdatingSelection.value = false;
    }
  }, 100);
};
// 跳转适用机构页面
const handleOrganizationJump = () => {
  router.push({
    path: "/coupon/management/detail/organization",
    query: {
      id: route.query.id
    }
  });
};

// 处理机构搜索
const handleOrganizationSearch = () => {
  organizationSearchName.value = editSearchForm.name;
  organizationSearchAlias.value = editSearchForm.alias;
  getOrganizationList(true);
};

// 重置机构搜索
const handleOrganizationReset = () => {
  editSearchForm.name = "";
  editSearchForm.alias = "";
  organizationSearchName.value = "";
  organizationSearchAlias.value = "";
  getOrganizationList(true);
};

// 处理机构分页变化
const handleOrganizationPageChange = (page) => {
  organizationPagination.value.currentPage = page;
  getOrganizationList();
};

// 处理机构每页大小变化
const handleOrganizationSizeChange = (size) => {
  organizationPagination.value.pageSize = size;
  getOrganizationList(true);
};

// 保存编辑
const handleSaveEdit = async () => {
  if (selectedOrganizationIds.value.length === 0) {
    ElMessage.warning("请至少选择一个机构");
    return;
  }

  const couponId = route.query.id;
  if (!couponId) {
    ElMessage.error("优惠券ID不存在");
    return;
  }

  try {
    loading.value = true;

    const requestData = {
      couponId: parseInt(couponId),
      organizationIds: selectedOrganizationIds.value
    };

    const operateLog = {
      operateLogType: "COUPON_MANAGEMENT",
      // operatorTarget: `"${couponDetail.value.couponName}"`,
      operateType: `编辑了优惠券“${couponDetail.value.couponName}”的适用机构范围`
    };

    console.log("保存请求数据:", requestData);

    try {
      const { code, data, msg } = await updateCouponOrganization(requestData, operateLog);
      if (code === 200) {
        ElMessage.success("保存成功");
        // 返回上一页
        setTimeout(() => {
          router.go(-1);
        }, 800);
      } else {
        ElMessage.error("保存编辑失败:", msg);
      }
      // ElMessage.success("保存成功");
      // 返回上一页
      // setTimeout(() => {
      //   router.go(-1);
      // }, 800);
    } catch (error) {
      console.error("保存编辑失败:", error);
      ElMessage.error("保存失败，请重试");
    }
  } catch (error) {
    console.error("保存编辑失败:", error);
    ElMessage.error("保存失败，请重试");
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  // 获取路由参数中的优惠券ID
  const couponId = route.query.id;
  if (couponId) {
    loadCouponDetail(couponId);
  }

  // 根据模式加载不同的数据
  if (isEditMode.value) {
    // 编辑模式：加载机构列表
    getOrganizationList(true);
  } else {
    // 详情模式：加载优惠券详情和表格数据
    loadTableData();
  }
});
</script>

<template>
  <div class="coupon-detail">
    <!-- 优惠券详情展示区域 -->
    <div class="common detail-container">
      <div class="detail-card">
        <div
          v-for="(item, index) in detailConfig"
          :key="index"
          class="detail-row"
        >
          <div class="detail-item detail-content">
            <span class="label">{{ item.label1 }}：</span>
            <span class="value">{{ couponDetail[item.prop1] }}</span>
          </div>
          <div class="detail-item">
            <span class="label">{{ item.label2 }}：</span>
            <span class="value">{{ couponDetail[item.prop2] }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="common table-container">
      <!-- 详情模式：原有的搜索区域 -->
      <div v-if="!isEditMode" class="search-section">
        <div class="coupon-search-container">
          <div class="search-items">
            <div class="search-item">
              <span class="search-label">用户账号 </span>
              <el-input
                v-model="searchForm.userAccount"
                placeholder="请输入用户账号"
                style="width: 200px"
                clearable
              />
            </div>
            <div class="search-item">
              <span class="search-label">优惠券状态 </span>
              <el-select
                v-model="searchForm.couponState"
                placeholder="请选择状态"
                style="width: 160px"
                clearable
              >
                <el-option label="全部" value="all" />
                <el-option label="未使用" value="NOT_USED" />
                <el-option label="已使用" value="USED" />
                <el-option label="已过期" value="EXPIRED" />
              </el-select>
            </div>
          </div>
          <div class="search-buttons">
            <el-button type="primary" @click="handleOrganizationJump">查看适用机构</el-button>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </div>
        </div>
      </div>

      <!-- 编辑模式：机构搜索区域 -->
      <div v-if="isEditMode" class="search-section">
        <div class="coupon-search-container">
          <div class="search-items">
            <div class="search-item">
              <span class="search-label">机构名称 </span>
              <el-input
                v-model="editSearchForm.name"
                placeholder="请输入机构名称"
                style="width: 200px"
                clearable
                @keyup.enter="handleOrganizationSearch"
              />
            </div>
            <div class="search-item">
              <span class="search-label">机构别名 </span>
              <el-input
                v-model="editSearchForm.alias"
                placeholder="请输入机构别名"
                style="width: 200px"
                clearable
                @keyup.enter="handleOrganizationSearch"
              />
            </div>
          </div>
          <div class="search-buttons">
            <el-button type="primary" @click="handleOrganizationSearch">搜索</el-button>
            <el-button @click="handleOrganizationReset">重置</el-button>
          </div>
        </div>
      </div>

      <!-- 详情模式：标题和统计信息 -->
      <div v-if="!isEditMode" class="title-statistics">
        <div class="table-title">
          <h3>领取记录</h3>
        </div>
        <div class="statistics">
          <span class="stat-item">发放数量：{{ statistics.issued }}</span>
          <span class="stat-item">领取数量：{{ statistics.received }}</span>
          <span class="stat-item">使用数量：{{ statistics.used }}</span>
        </div>
      </div>

      <!-- 编辑模式：标题 -->
      <div v-if="isEditMode" class="title-statistics">
        <div class="table-title">
          <h3>适用机构选择</h3>
        </div>
        <div class="statistics">
          <span class="stat-item">已选择机构：{{ selectedOrganizations.length }}</span>
        </div>
      </div>

      <!-- 详情模式：领取记录表格 -->
      <div v-if="!isEditMode" class="table-section">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 180 }"
          align-whole="left"
          table-layout="auto"
          :data="tableData"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="loadTableData"
          @page-current-change="loadTableData"
        >
          <template #phone="{ row }">
            <div v-if="row.phone" class="phone-display">
              <span class="phone-text">
                {{
                  row.isPhoneVisible
                    ? decrypt(row.phone)
                    : row.phoneCt || row.phone
                }}
              </span>
              <el-icon
                v-if="row.phoneCt"
                class="phone-toggle-icon"
                @click="togglePhoneDisplay(row)"
              >
                <component :is="row.isPhoneVisible ? View : Hide" />
              </el-icon>
            </div>
            <span v-else>--</span>
          </template>
          <template #operation="{ row }">
            <div v-if="row.orderId" class="operation-buttons">
              <el-button
                class="reset-margin"
                link
                type="primary"
                @click="handleRelateOrder(row)"
              >
                关联订单
              </el-button>
            </div>
          </template>
        </pure-table>
      </div>

      <!-- 编辑模式：机构选择表格 -->
      <div v-if="isEditMode" class="table-section">
        <pure-table
          ref="organizationTableRef"
          row-key="id"
          reserve-selection
          :height="400"
          adaptive
          :adaptiveConfig="{ offsetBottom: 180 }"
          align-whole="left"
          table-layout="auto"
          :loading="organizationLoading"
          :data="organizationList"
          :columns="editColumns"
          :pagination="organizationPagination"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleOrganizationSelectionChange"
          @page-size-change="handleOrganizationSizeChange"
          @page-current-change="handleOrganizationPageChange"
        />
      </div>

      <!-- 详情模式：返回按钮 -->
      <div v-if="!isEditMode" class="footer-actions">
        <el-button @click="handleBack">返回</el-button>
      </div>

      <!-- 编辑模式：确定和取消按钮 -->
      <div v-if="isEditMode" class="footer-actions">
        <el-button @click="handleBack">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSaveEdit">确定</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.coupon-detail {
  height: 88vh;
  display: flex;
  flex-direction: column;
  .common {
    padding: 20px;
    background-color: #fff;
    margin-bottom: 20px;

    &.table-container {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

  .detail-card {
    padding: 4px 0;
  }

  .detail-row {
    display: flex;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }
  .detail-content {
    margin-left: 50px;
  }
  .detail-item {
    flex: 1;
    display: flex;
    align-items: center;

    .label {
      color: #606266;
      font-size: 14px;
      min-width: 120px;
      flex-shrink: 0;
      text-align: right;
    }

    .value {
      color: #303133;
      font-size: 14px;
      font-weight: 500;
      text-align: left;
      margin-left: 20px;
    }
  }

  .search-section {
    margin-bottom: 20px;
    flex-shrink: 0;
  }

  .coupon-search-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .search-label {
      margin-right: 14px;
      font-size: 14px;
      font-weight: 700;
      color: rgb(96, 98, 102);
    }
  }

  .search-items {
    display: flex;
    gap: 20px;
    align-items: center;
  }

  .search-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .search-label {
      font-size: 14px;
      color: #606266;
      white-space: nowrap;
    }
  }

  .title-statistics {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e4e7ed;
    flex-shrink: 0;
  }

  .table-title {
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .statistics {
    display: flex;
    gap: 40px;

    .stat-item {
      font-size: 14px;
      color: #303133;
      font-weight: 500;
    }
  }

  .table-section {
    flex: 1;
    overflow: hidden;
  }

  .operation-buttons {
    display: flex;
    gap: 8px;
  }

  .footer-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }

  .phone-display {
    display: flex;
    align-items: center;
    gap: 8px;

    .phone-text {
      min-width: 90px;
    }

    .phone-toggle-icon {
      cursor: pointer;
      font-size: 16px;
    }
  }
}
:deep(.el-pagination) {
  .el-pagination__sizes {
    margin-right: 15px;
  }
  .el-pager li {
    background-color: #f0f2f5;
    border-radius: 2px;
    margin: 0 4px;
  }

  .el-pager li.is-active {
    background-color: #409eff;
    color: #fff;
    border-color: #409eff;
  }
  .el-pagination__jump,
  .el-pagination__sizes {
    .el-input__inner {
      background-color: #fff;
    }
  }
  .btn-prev,
  .btn-next {
    background-color: #f0f2f5;
    border-radius: 2px;
    margin: 0 4px;
  }
  .btn-prev:hover,
  .btn-next:hover {
    color: #409eff;
  }
}
</style>
