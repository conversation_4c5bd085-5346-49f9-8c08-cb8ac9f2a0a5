<script setup>
import { onMounted } from "vue";
import RegistrationManagement from "./utils/RegistrationManagement.jsx";
import { PlusSearch, PlusTable } from "plus-pro-components";

const {
  tableHeight,
  loading,
  tabs,
  activeTab,
  form,
  tableData,
  pagination,
  searchColumns,
  tableColumns,
  buttons,
  exportLoading,
  onSearch,
  onReset,
  onTabChange,
  onPageChange,
  onSizeChange,
  onExportRow,
  initData
} = RegistrationManagement();

onMounted(() => {
  // 首次加载列表
  initData();
});
</script>

<template>
  <div class="page-container">
    <div class="containers">
      <div class="con_search">
        <PlusSearch
          v-model="form"
          :columns="searchColumns"
          :show-number="5"
          :label-width="80"
          :label-suffix="''"
          :hasUnfold="false"
          :searchOnChange="false"
          @search="onSearch"
          @reset="onReset"
        />
      </div>

      <div class="tabs">
        <el-tabs v-model="activeTab" @tab-change="onTabChange">
          <el-tab-pane
            v-for="t in tabs"
            :key="t.value"
            :label="t.label"
            :name="t.value"
          />
        </el-tabs>
      </div>

      <div style="width: 100%; padding: 0px 20px; background-color: #fff">
        <PlusTable
          v-loading="loading"
          :data="tableData"
          :columns="tableColumns"
          :action-bar="{
            buttons,
            align: 'center'
          }"
          :title-bar="false"
          :height="tableHeight"
          class="no-border-table"
          :border="false"
        />
      </div>

      <div class="pagination">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="pagination.page"
          :page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 15, 20, 30, 50]"
          @size-change="onSizeChange"
          @current-change="onPageChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.page-container {
  height: 100%;
}
.con_search {
  padding: 18px 22px;
  background: #fff;

  /* 隐藏搜索和重置按钮上的图标 */
  :deep(.el-form-item__content .el-button .el-icon) {
    display: none;
    width: 0;
    height: 0;
  }

  :deep(.el-form-item__content) {
    .el-button {
      span {
        margin-left: 0px !important;
      }
    }
  }
}
.input_width {
  width: 220px;
}
.tabs {
  padding: 10px 20px;
  margin-top: 10px;
  background-color: #fff;
}
.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px;
  background-color: #fff;
}
/*去掉tabs底部的下划线*/
::v-deep .el-tabs__nav-wrap::after {
  position: static !important;
}
.red-text {
  color: rgb(248, 123, 123);
}
.blue-text {
  color: rgb(84, 157, 253);
}
:deep(.el-form-item) {
  margin-bottom: 5px;
}
:deep(.el-col) {
  max-width: 330px !important;
}
/* 表头颜色为白色 */
:deep(.el-table__header th) {
  background-color: #fafafa !important;
  color: #565353;
}
/* 表格操作栏样式 */
:deep(.w-e-text-placeholder) {
  top: 6px;
  left: 14px;
}
:deep(.el-button:focus-visible) {
  display: none;
}
:deep(.el-link__inner) {
  margin: 5px;
  color: #409eff;
}

:deep(.el-link::after) {
  border: none !important;
}
</style>
