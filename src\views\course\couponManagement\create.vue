<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { couponSave, couponFindById, courseTypeFindAll, organizationFindAll } from "@/api/coupon";
import { removeEmptyValues } from "@iceywu/utils";
import { requestTo } from "@/utils/http/tool";

defineOptions({
  name: "CouponManagementCreate"
});

const router = useRouter();
const route = useRoute();

// 表单数据 - 修改为与API匹配的字段名
const formData = ref({
  name: "", // 优惠券名称
  couponDiscountType: "FULL_REDUCTION", // 优惠券类型，默认选中满减券
  conditionAmount: "", // 满减条件金额
  discountAmount: "", // 优惠金额
  totalIssue: "", // 发放数量
  distributionStartTime: "", // 发放开始时间
  distributionEndTime: "", // 发放结束时间
  isUseLimit: true, // 生效是否有限制
  startTime: "", // 生效开始时间
  endTime: "", // 生效结束时间
  remarks: "", // 备注
  enabled: true, // 状态开关
  isLimitCourseType: false, // 课程类型限制，默认为全部
  courseTypeIds: [], // 选中的课程类型ID数组
  isLimitOrganization: false, // 适用机构限制，默认为通用
  organizationIds: [], // 选中的机构ID数组
  _currentEditingField: null // 当前正在编辑的字段
});

// 表单验证规则 - 修改为与API匹配的字段名
const formRules = reactive({
  name: [
    { required: true, message: "请输入优惠券名称", trigger: "blur" },
    { max: 10, message: "优惠券名称不能超过10个字符", trigger: "blur" }
  ],
  couponDiscountType: [
    { required: true, message: "请选择优惠券类型", trigger: "change" }
  ],
  conditionAmount: [
    { required: true, message: "请输入满减条件金额", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value === null || value === undefined || value === "") {
          callback(new Error("请输入满减条件金额"));
        } else if (value <= 0) {
          callback(new Error("满减条件金额必须大于0"));
        } else if (
          formData.value.discountAmount > 0 &&
          value < formData.value.discountAmount &&
          formData.value._currentEditingField === "conditionAmount"
        ) {
          callback(new Error("减额不能大于满额，请调整设置"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  discountAmount: [
    { required: true, message: "请输入优惠金额", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value === null || value === undefined || value === "") {
          callback(new Error("请输入优惠金额"));
        } else if (value <= 0) {
          callback(new Error("优惠金额必须大于0"));
        } else if (
          formData.value.conditionAmount > 0 &&
          value > formData.value.conditionAmount &&
          formData.value._currentEditingField === "discountAmount"
        ) {
          callback(new Error("减额不能大于满额，请调整设置"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  totalIssue: [
    { required: true, message: "请输入发放数量", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value < 1) {
          callback(new Error("发放数量不能小于1"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  distributionStartTime: [
    {
      validator: (_rule, value, callback) => {
        // 只在发放开始时间字段验证时提示
        const startTime = value;
        const endTime = formData.value.distributionEndTime;

        if (startTime && endTime) {
          if (new Date(startTime) >= new Date(endTime)) {
            callback(new Error("开始时间必须早于结束时间"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
  distributionEndTime: [
    { required: true, message: "请选择发放结束时间", trigger: "blur" },
    {
      validator: (_rule, value, callback) => {
        // 只在发放结束时间字段验证时提示
        const startTime = formData.value.distributionStartTime;
        const endTime = value;

        if (startTime && endTime) {
          if (new Date(endTime) <= new Date(startTime)) {
            callback(new Error("结束时间必须晚于开始时间"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  startTime: [
    {
      validator: (_rule, value, callback) => {
        // 验证生效开始时间不能早于发放开始时间
        if (value && formData.value.distributionStartTime) {
          if (
            new Date(value) < new Date(formData.value.distributionStartTime)
          ) {
            callback(new Error("生效开始时间不能早于发放开始时间"));
            return;
          }
        }

        // 只在生效开始时间字段验证时提示
        const startTime = value;
        const endTime = formData.value.endTime;

        if (startTime && endTime) {
          if (new Date(startTime) >= new Date(endTime)) {
            callback(new Error("开始时间必须早于结束时间"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
  endTime: [
    { required: true, message: "请选择生效结束时间", trigger: "blur" },
    {
      validator: (_rule, value, callback) => {
        // 当生效时间有限制时，结束时间为必填
        if (formData.value.isUseLimit && !value) {
          callback(new Error("请选择生效结束时间"));
          return;
        }

        if (value) {
          // 验证生效结束时间不能早于发放结束时间
          if (
            formData.value.distributionEndTime &&
            new Date(value) < new Date(formData.value.distributionEndTime)
          ) {
            callback(new Error("生效结束时间不能早于发放结束时间"));
            return;
          }

          // 只在生效结束时间字段验证时提示
          const startTime = formData.value.startTime;
          const endTime = value;

          if (startTime && endTime) {
            if (new Date(endTime) <= new Date(startTime)) {
              callback(new Error("结束时间必须晚于开始时间"));
            } else {
              callback();
            }
          } else {
            callback();
          }
        } else {
          // 当生效时间不限制时，允许为空
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  remarks: [{ max: 200, message: "备注不能超过200个字符", trigger: "blur" }],
  isLimitCourseType: [
    { required: true, message: "请选择课程类型", trigger: "change" }
  ],
  courseTypeIds: [
    {
      validator: (rule, value, callback) => {
        if (formData.value.isLimitCourseType && (!value || value.length === 0)) {
          callback(new Error("请至少选择一个课程类型"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
  isLimitOrganization: [
    { required: true, message: "请选择适用机构", trigger: "change" }
  ],
  organizationIds: [
    {
      validator: (rule, value, callback) => {
        if (formData.value.isLimitOrganization && (!value || value.length === 0)) {
          callback(new Error("请至少选择一个机构"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ]
});
//优惠券类型选项
const couponDiscountTypeOptions = [
  { label: "满减券", value: "FULL_REDUCTION" }
];
// 生效时间类型选项
const useTimeOptions = [
  { label: "不限", value: false },
  { label: "有限", value: true }
];

// 状态选项
const statusOptions = [
  { label: "启用", value: true },
  { label: "停用", value: false }
];

// 课程类型选择选项
const courseTypeOptions = [
  { label: "全部", value: false },
  { label: "指定", value: true }
];

// 课程类型列表
const courseTypeList = ref([]);

// 适用机构选择选项
const organizationOptions = [
  { label: "通用", value: false },
  { label: "指定", value: true }
];

// 机构列表和分页
const organizationList = ref([]);
const organizationPagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  background: true
});
const organizationLoading = ref(false);
const organizationSearchName = ref("");
const organizationSearchAlias = ref("");

// 机构选择状态持久化管理
const selectedOrganizations = ref([]); // 已选中的机构数据
const selectedOrganizationIds = ref([]); // 已选中的机构ID列表
const isUpdatingSelection = ref(false); // 防止递归更新标志

// 机构表格列配置
const organizationColumns = [
  {
    type: "selection",
    width: 55,
    align: "center"
  },
  {
    label: "机构名称",
    prop: "name",
    minWidth: 200,
    formatter: ({ name }) => name || "--"
  },
  {
    label: "机构别名",
    prop: "alias",
    minWidth: 150,
    formatter: ({ alias }) => alias || "--"
  },
  {
    label: "机构ID",
    prop: "id",
    width: 100,
    align: "center"
  }
];

const formRef = ref();
const loading = ref(false);
const organizationTableRef = ref();

// 获取课程类型列表
const getCourseTypeList = async () => {
  try {
    const [err, result] = await requestTo(courseTypeFindAll({ depth: 1 }));
    if (result && result.content) {
      courseTypeList.value = result.content;
    }
  } catch (error) {
    console.error("获取课程类型失败:", error);
  }
};

// 获取机构列表
const getOrganizationList = async (resetPage = false) => {
  if (resetPage) {
    organizationPagination.value.currentPage = 1;
  }

  organizationLoading.value = true;
  try {
    const params = {
      page: organizationPagination.value.currentPage - 1, // 后端从0开始
      size: organizationPagination.value.pageSize,
      freeze: false // 无论什么情况都传递 freeze: false
    };

    // 添加搜索条件
    if (organizationSearchName.value.trim()) {
      params.name = organizationSearchName.value.trim();
    }
    if (organizationSearchAlias.value.trim()) {
      params.alias = organizationSearchAlias.value.trim();
    }

    const [err, result] = await requestTo(organizationFindAll(params));
    if (result && result.content) {
      organizationList.value = result.content;
      organizationPagination.value.total = result.totalElements || 0;
      // 设置表格选中状态（用于回显）
      setOrganizationTableSelection();
    }
  } catch (error) {
    console.error("获取机构列表失败:", error);
  } finally {
    organizationLoading.value = false;
  }
};

// 处理课程类型选择变化
const handleCourseTypeLimitChange = () => {
  // 清空已选择的课程类型
  formData.value.courseTypeIds = [];

  if (formRef.value) {
    // 清除课程类型ID的验证状态
    formRef.value.clearValidate("courseTypeIds");
    // 验证课程类型限制字段
    formRef.value.validateField("isLimitCourseType");
  }
};

// 处理课程类型复选框变化
const handleCourseTypeIdsChange = () => {
  if (formRef.value) {
    formRef.value.validateField("courseTypeIds");
  }
};

// 处理适用机构选择变化
const handleOrganizationLimitChange = () => {
  // 清空已选择的机构
  formData.value.organizationIds = [];
  selectedOrganizations.value = [];
  selectedOrganizationIds.value = [];

  if (formRef.value) {
    // 清除机构ID的验证状态
    formRef.value.clearValidate("organizationIds");
    // 验证适用机构限制字段
    formRef.value.validateField("isLimitOrganization");
  }

  // 如果选择指定机构，则加载机构列表
  if (formData.value.isLimitOrganization) {
    getOrganizationList(true);
  }
};

// 处理机构表格选择变化（优化版）
const handleOrganizationSelectionChange = (selection) => {
  // 防止递归更新
  if (isUpdatingSelection.value) {
    return;
  }

  // 获取当前页面的机构ID列表
  const currentPageIds = organizationList.value.map(item => item.id);

  // 从已选中列表中移除当前页面的机构
  const otherPagesSelected = selectedOrganizations.value.filter(
    item => !currentPageIds.includes(item.id)
  );

  // 合并当前页面的选中项和其他页面的选中项
  selectedOrganizations.value = [...otherPagesSelected, ...selection];

  // 更新ID列表
  selectedOrganizationIds.value = selectedOrganizations.value.map(item => item.id);

  // 同步到表单数据
  formData.value.organizationIds = [...selectedOrganizationIds.value];

  // 验证表单
  if (formRef.value) {
    formRef.value.validateField("organizationIds");
  }

  console.log('选中机构数量:', selectedOrganizations.value.length);
  console.log('选中机构ID:', selectedOrganizationIds.value);
};

// 设置机构表格的选中状态（优化版）
const setOrganizationTableSelection = () => {
  if (!organizationTableRef.value || selectedOrganizationIds.value.length === 0) {
    return;
  }

  // 设置更新标志，防止递归触发
  isUpdatingSelection.value = true;

  setTimeout(() => {
    try {
      // 找到当前页面中需要选中的行
      const selectedRows = organizationList.value.filter(item =>
        selectedOrganizationIds.value.includes(item.id)
      );

      // 获取表格实例
      const tableInstance = organizationTableRef.value.getTableRef();
      if (tableInstance) {
        // 清空当前选中状态
        tableInstance.clearSelection();

        // 设置选中状态
        selectedRows.forEach(row => {
          tableInstance.toggleRowSelection(row, true);
        });
      }
    } catch (error) {
      console.error('设置表格选中状态失败:', error);
    } finally {
      // 重置更新标志
      isUpdatingSelection.value = false;
    }
  }, 100);
};

// 处理机构搜索
const handleOrganizationSearch = () => {
  getOrganizationList(true);
};

// 重置机构搜索
const handleOrganizationReset = () => {
  organizationSearchName.value = "";
  organizationSearchAlias.value = "";
  getOrganizationList(true);
};

// 处理机构分页变化
const handleOrganizationPageChange = (page) => {
  organizationPagination.value.currentPage = page;
  getOrganizationList();
};

// 处理机构每页大小变化
const handleOrganizationSizeChange = (size) => {
  organizationPagination.value.pageSize = size;
  getOrganizationList(true);
};

// 时间转换为时间戳的工具函数
const convertToTimestamp = dateValue => {
  if (!dateValue) return 0;
  return new Date(dateValue).getTime();
};

// 获取当前时间戳
const getCurrentTimestamp = () => {
  return new Date().getTime();
};

// 提交表单
const handleSubmit = () => {
  formRef.value.validate(async (valid, fields) => {
    if (valid) {
      // 时间验证已移至表单验证规则中，此处不再需要额外验证

      loading.value = true;

      try {
        // 构建API请求数据
        const operateLog = {
          operateLogType: "COUPON_MANAGEMENT",
          operatorTarget: `“${formData.value.name}”`
        };
        const requestData = {
          name: formData.value.name,
          couponDiscountType: formData.value.couponDiscountType, // 传递选中的优惠券类型
          conditionAmount: formData.value.conditionAmount,
          discountAmount: formData.value.discountAmount,
          totalIssue: formData.value.totalIssue,
          // 如果发放开始时间为空，则生效当前时间戳
          distributionStartTime: formData.value.distributionStartTime
            ? convertToTimestamp(formData.value.distributionStartTime)
            : getCurrentTimestamp(),
          distributionEndTime: convertToTimestamp(
            formData.value.distributionEndTime
          ),
          isUseLimit: formData.value.isUseLimit,
          remarks: formData.value.remarks || "",
          couponScope: "ALL", // 固定传"ALL"
          enabled: formData.value.enabled,
          isLimitCourseType: formData.value.isLimitCourseType, // 课程类型限制
          courseTypeIds: formData.value.isLimitCourseType ? formData.value.courseTypeIds : [], // 选中的课程类型ID
          isLimitOrganization: formData.value.isLimitOrganization, // 适用机构限制
          organizationIds: formData.value.isLimitOrganization ? [...new Set(selectedOrganizationIds.value)] : [] // 去重后的机构ID
        };

        // 只有当isUseLimit为true时才传递生效时间
        if (formData.value.isUseLimit) {
          // 如果生效开始时间为空，则生效当前时间戳
          requestData.startTime = formData.value.startTime
            ? convertToTimestamp(formData.value.startTime)
            : getCurrentTimestamp();
          requestData.endTime = convertToTimestamp(formData.value.endTime);
        }

        console.log("提交的API数据:", requestData);
        console.log("去重后的机构ID数量:", requestData.organizationIds.length);

        // 调用API
        const { code, data, msg } = await couponSave(
          removeEmptyValues(requestData),
          operateLog
        );
        if (code === 200) {
          ElMessage.success("优惠券创建成功");
          router.push("/coupon/management/index");
        } else {
          ElMessage.error("创建优惠券失败:", msg);
        }
      } catch (error) {
        ElMessage.error("创建优惠券失败，请重试");
      } finally {
        loading.value = false;
      }
    } else {
      // 显示具体的验证错误信息
      const errorMessages = [];
      for (const field in fields) {
        if (fields[field] && fields[field].length > 0) {
          errorMessages.push(fields[field][0].message);
        }
      }
      if (errorMessages.length > 0) {
        ElMessage.error(errorMessages[0]); // 显示第一个错误信息
      } else {
        ElMessage.error("请完善表单信息");
      }
    }
  });
};

// 处理满减条件金额获得焦点
const handleConditionAmountFocus = () => {
  formData.value._currentEditingField = "conditionAmount";
  // 清除另一个输入框的错误提示
  if (formRef.value) {
    formRef.value.clearValidate("discountAmount");
  }
};

// 处理满减条件金额变化
const handleConditionAmountChange = () => {
  // 标记当前编辑字段并触发验证
  formData.value._currentEditingField = "conditionAmount";
  if (formRef.value) {
    // 验证当前字段
    formRef.value.validateField("conditionAmount");
    // 如果优惠金额有值，也触发其验证但不显示比较错误
    if (formData.value.discountAmount) {
      setTimeout(() => {
        formData.value._currentEditingField = null;
        formRef.value.validateField("discountAmount");
      }, 0);
    }
  }
};

// 处理优惠金额获得焦点
const handleDiscountAmountFocus = () => {
  formData.value._currentEditingField = "discountAmount";
  // 清除另一个输入框的错误提示
  if (formRef.value) {
    formRef.value.clearValidate("conditionAmount");
  }
};

// 处理优惠金额变化
const handleDiscountAmountChange = () => {
  // 标记当前编辑字段并触发验证
  formData.value._currentEditingField = "discountAmount";
  if (formRef.value) {
    // 验证当前字段
    formRef.value.validateField("discountAmount");
    // 如果满减条件金额有值，也触发其验证但不显示比较错误
    if (formData.value.conditionAmount) {
      setTimeout(() => {
        formData.value._currentEditingField = null;
        formRef.value.validateField("conditionAmount");
      }, 0);
    }
  }
};

// 处理发放开始时间变化
const handleDistributionStartTimeChange = () => {
  if (formRef.value) {
    // 清除相关字段的验证状态
    formRef.value.clearValidate([
      "distributionStartTime",
      "distributionEndTime"
    ]);
    // 延迟验证，确保数据已更新，只验证当前操作的字段
    setTimeout(() => {
      formRef.value.validateField("distributionStartTime");
      // 如果另一个字段之前有错误，清除其错误状态
      if (formData.value.distributionEndTime) {
        formRef.value.clearValidate("distributionEndTime");
      }

      // 当发放开始时间变化时，同时验证使用开始时间
      // 因为使用开始时间不能早于发放开始时间
      if (formData.value.isUseLimit && formData.value.startTime) {
        formRef.value.validateField("startTime");
      }
    }, 0);
  }
};

// 处理发放结束时间变化
const handleDistributionEndTimeChange = () => {
  if (formRef.value) {
    // 清除相关字段的验证状态
    formRef.value.clearValidate([
      "distributionStartTime",
      "distributionEndTime"
    ]);
    // 延迟验证，确保数据已更新，只验证当前操作的字段
    setTimeout(() => {
      formRef.value.validateField("distributionEndTime");
      // 如果另一个字段之前有错误，清除其错误状态
      if (formData.value.distributionStartTime) {
        formRef.value.clearValidate("distributionStartTime");
      }

      // 当发放结束时间变化时，同时验证使用结束时间
      // 因为使用结束时间不能早于发放结束时间
      if (formData.value.isUseLimit && formData.value.endTime) {
        formRef.value.validateField("endTime");
      }
    }, 0);
  }
};

// 处理生效开始时间变化
const handleStartTimeChange = () => {
  if (formRef.value) {
    // 清除相关字段的验证状态
    formRef.value.clearValidate(["startTime", "endTime"]);
    // 延迟验证，确保数据已更新，只验证当前操作的字段
    setTimeout(() => {
      formRef.value.validateField("startTime");
      // 如果另一个字段之前有错误，清除其错误状态
      if (formData.value.endTime) {
        formRef.value.clearValidate("endTime");
        // 当使用开始时间变化时，同时验证使用结束时间
        // 因为使用结束时间必须晚于使用开始时间
        formRef.value.validateField("endTime");
      }
    }, 0);
  }
};

// 处理生效结束时间变化
const handleEndTimeChange = () => {
  if (formRef.value) {
    // 清除相关字段的验证状态
    formRef.value.clearValidate(["startTime", "endTime"]);
    // 延迟验证，确保数据已更新，只验证当前操作的字段
    setTimeout(() => {
      formRef.value.validateField("endTime");
      // 如果另一个字段之前有错误，清除其错误状态
      if (formData.value.startTime) {
        formRef.value.clearValidate("startTime");
      }
    }, 0);
  }
};

// 取消操作
const handleCancel = () => {
  ElMessageBox.confirm(
    "确定要取消新建优惠券吗？未保存的数据将丢失。",
    "确认取消",
    {
      confirmButtonText: "确定",
      cancelButtonText: "继续编辑",
      type: "warning"
    }
  )
    .then(() => {
      router.push("/coupon/management/index");
    })
    .catch(() => {
      // 用户选择继续编辑
    });
};

// 加载优惠券详情数据
const loadCouponDetail = async id => {
  try {
    const [err, result] = await requestTo(couponFindById({ id }));

    if (err) {
      ElMessage.error("获取优惠券详情失败");
      return;
    }

    if (result) {
      // 将获取到的数据回显到表单中
      formData.value = {
        name: result.name || "",
        couponDiscountType: result.couponDiscountType || "FULL_REDUCTION",
        conditionAmount: result.conditionAmount || 0,
        discountAmount: result.discountAmount || 0,
        totalIssue: result.totalIssue || 1,
        distributionStartTime: result.distributionStartTime
          ? new Date(result.distributionStartTime)
          : "",
        distributionEndTime: result.distributionEndTime
          ? new Date(result.distributionEndTime)
          : "",
        isUseLimit: result.isUseLimit || false,
        startTime: result.startTime ? new Date(result.startTime) : "",
        endTime: result.endTime ? new Date(result.endTime) : "",
        remarks: result.remarks || "",
        enabled: result.enabled !== undefined ? result.enabled : true,
        // 课程类型回显逻辑
        isLimitCourseType: result.courseTypes && result.courseTypes.length > 0,
        courseTypeIds: result.courseTypes ? result.courseTypes.map(item => item.id) : [],
        // 适用机构回显逻辑
        isLimitOrganization: result.organizations && result.organizations.length > 0,
        organizationIds: result.organizations ? result.organizations.map(item => item.id) : [],
        _currentEditingField: null
      };

      // 如果有机构数据需要回显，初始化持久化状态并加载机构列表
      if (formData.value.isLimitOrganization && formData.value.organizationIds.length > 0) {
        // 初始化持久化状态
        if (result.organizations && result.organizations.length > 0) {
          selectedOrganizations.value = [...result.organizations];
          selectedOrganizationIds.value = result.organizations.map(item => item.id);
        }

        // 延迟加载，确保表单数据已经设置完成
        setTimeout(() => {
          getOrganizationList(true);
        }, 100);
      }
    }
  } catch (error) {
    console.error("加载优惠券详情失败:", error);
    ElMessage.error("加载优惠券详情失败");
  }
};

onMounted(() => {
  // 获取课程类型列表
  getCourseTypeList();

  // 检查路由参数，如果是复制操作且有ID，则加载优惠券详情
  const { type, id } = route.query;

  if (type === "copy" && id) {
    loadCouponDetail(id);
  }
});
</script>

<template>
  <div class="coupon-create">
    <div class="common">
      <el-scrollbar class="scroll-container">
      <div class="section-title">优惠券基础信息</div>
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          class="coupon-form"
        >
        <div class="form-section">
          <el-form-item
            label="优惠券名称"
            prop="name"
            style="margin-bottom: 30px"
          >
            <el-input
              v-model="formData.name"
              maxlength="10"
              show-word-limit
              placeholder="请输入优惠券名称"
              style="width: 400px"
            />
          </el-form-item>

          <el-form-item
            label="课程类型"
            prop="isLimitCourseType"
            style="margin-bottom: 20px"
          >
            <el-radio-group
              v-model="formData.isLimitCourseType"
              @change="handleCourseTypeLimitChange"
            >
              <el-radio
                v-for="option in courseTypeOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="formData.isLimitCourseType"
            label=" "
            prop="courseTypeIds"
            style="margin-bottom: 30px"
          >
            <div class="course-type-selection">
              <el-checkbox-group
                v-model="formData.courseTypeIds"
                @change="handleCourseTypeIdsChange"
              >
                <el-checkbox
                  v-for="courseType in courseTypeList"
                  :key="courseType.id"
                  :label="courseType.id"
                  class="course-type-checkbox"
                >
                  {{ courseType.name }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </el-form-item>

          <el-form-item
            label="优惠券类型"
            prop="couponDiscountType"
            style="margin-bottom: 30px"
          >
            <el-radio-group v-model="formData.couponDiscountType">
              <el-radio
                v-for="option in couponDiscountTypeOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <div class="form-item-group">
            <el-form-item
              label="满减金额"
              prop="conditionAmount"
              class="inline-form-item"
            >
              <div class="discount-input-group">
                <span class="form-text">满</span>
                <el-input
                  v-model.number="formData.conditionAmount"
                  type="number"
                  oninput="if(value !== '') { value=value.replace(/[^\d]/g,''); if(value > 1000000) value = 1000000}"
                  placeholder="请输入满减条件金额"
                  style="width: 180px"
                  @focus="handleConditionAmountFocus"
                  @change="handleConditionAmountChange"
                />
              </div>
            </el-form-item>

            <el-form-item
              label=" "
              prop="discountAmount"
              class="inline-form-item"
            >
              <div class="discount-input-group">
                <span class="form-text">减</span>
                <el-input
                  v-model.number="formData.discountAmount"
                  type="number"
                  oninput="if(value !== '') { value=value.replace(/[^\d]/g,''); if(value > 1000000) value = 1000000}"
                  placeholder="请输入优惠金额"
                  style="width: 180px"
                  @focus="handleDiscountAmountFocus"
                  @change="handleDiscountAmountChange"
                />
              </div>
            </el-form-item>
          </div>

          <el-form-item
            label="发放数量"
            prop="totalIssue"
            style="margin-bottom: 30px"
          >
            <el-input
              v-model.number="formData.totalIssue"
              type="number"
              oninput="value=value.replace(/[^\d]/g,''); if(value > 1000000) value = 1000000"
              placeholder="请输入优惠券发放数量"
              style="width: 400px"
            />
          </el-form-item>

          <div class="form-item-group">
            <el-form-item
              label="发放时间"
              prop="distributionStartTime"
              class="inline-form-item"
            >
              <div class="time-range">
                <span>开始</span>
                <el-date-picker
                  v-model="formData.distributionStartTime"
                  placeholder="请选择开始时间"
                  style="width: 200px; margin: 0 10px"
                  @change="handleDistributionStartTimeChange"
                />
              </div>
            </el-form-item>

            <el-form-item
              label=" "
              prop="distributionEndTime"
              class="inline-form-item"
            >
              <div class="time-range">
                <span>结束</span>
                <el-date-picker
                  v-model="formData.distributionEndTime"
                  placeholder="请选择结束时间"
                  style="width: 200px; margin-left: 10px"
                  @change="handleDistributionEndTimeChange"
                />
              </div>
            </el-form-item>
          </div>

          <el-form-item
            label="生效时间"
            prop="isUseLimit"
            style="margin-bottom: 20px"
          >
            <el-radio-group v-model="formData.isUseLimit">
              <el-radio
                v-for="option in useTimeOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <div v-if="formData.isUseLimit" class="form-item-group">
            <el-form-item label=" " prop="startTime" class="inline-form-item">
              <div class="time-range">
                <span>开始</span>
                <el-date-picker
                  v-model="formData.startTime"
                  placeholder="请选择开始时间"
                  style="width: 200px; margin: 0 10px"
                  @change="handleStartTimeChange"
                />
              </div>
            </el-form-item>

            <el-form-item label=" " prop="endTime" class="inline-form-item">
              <div class="time-range">
                <span>结束</span>
                <el-date-picker
                  v-model="formData.endTime"
                  placeholder="请选择结束时间"
                  style="width: 200px; margin-left: 10px"
                  @change="handleEndTimeChange"
                />
              </div>
            </el-form-item>
          </div>

          <el-form-item label="备注" prop="remarks" style="margin-bottom: 30px">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="4"
              maxlength="200"
              show-word-limit
              placeholder="请输入备注信息"
              style="width: 400px"
            />
          </el-form-item>

          <el-form-item
            label="状态"
            prop="enabled"
            style="margin-bottom: 20px"
            required
          >
            <el-radio-group v-model="formData.enabled">
              <el-radio
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </el-form>

      <div class="section-title">优惠券适用机构</div>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="coupon-form"
      >
        <div class="form-section">
          <el-form-item
            label="适用机构"
            prop="isLimitOrganization"
            style="margin-bottom: 20px"
          >
            <el-radio-group
              v-model="formData.isLimitOrganization"
              @change="handleOrganizationLimitChange"
            >
              <el-radio
                v-for="option in organizationOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="formData.isLimitOrganization"
            label=" "
            prop="organizationIds"
            style="margin-bottom: 30px"
          >
            <div class="organization-selection">
              <div class="search-bar" style="margin-bottom: 16px">
                <el-form-item label="机构名称">
                  <el-input
                    v-model="organizationSearchName"
                    placeholder="请输入机构名称"
                    style="width: 200px; margin-right: 12px"
                    clearable
                    @keyup.enter="handleOrganizationSearch"
                  />
                </el-form-item>
                <el-form-item label="机构别名">
                  <el-input
                    v-model="organizationSearchAlias"
                    placeholder="请输入机构别名"
                    style="width: 200px; margin-right: 12px"
                    clearable
                    @keyup.enter="handleOrganizationSearch"
                  />
                </el-form-item>
                <el-button type="primary" @click="handleOrganizationSearch">
                  搜索
                </el-button>
                <el-button @click="handleOrganizationReset">
                  重置
                </el-button>
              </div>

              <div class="table-container">
                <pure-table
                  ref="organizationTableRef"
                  row-key="id"
                  reserve-selection
                  :height="400"
                  adaptive
                  :adaptiveConfig="{ offsetBottom: 108 }"
                  align-whole="left"
                  table-layout="auto"
                  :loading="organizationLoading"
                  :data="organizationList"
                  :columns="organizationColumns"
                  :pagination="organizationPagination"
                  :header-cell-style="{
                    background: 'var(--el-fill-color-light)',
                    color: 'var(--el-text-color-primary)'
                  }"
                  @selection-change="handleOrganizationSelectionChange"
                  @page-size-change="handleOrganizationSizeChange"
                  @page-current-change="handleOrganizationPageChange"
                />
              </div>
            </div>
          </el-form-item>
        </div>
      </el-form>
      </el-scrollbar>

      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确认
        </el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.coupon-create {
  .common {
    height: 88vh;
    padding: 20px;
    background-color: #fff;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .page-header {
    margin-bottom: 24px;

    h2 {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
      margin: 0;
    }
  }

  .scroll-container {
    flex: 1;
    height: calc(88vh - 120px);
  }

  .coupon-form {
    margin-left: 80px;
    padding: 20px 0;
  }

  .form-section {
    margin-top: 40px;
    margin-bottom: 32px;
  }

  .section-title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    margin: 20px 0 20px 10px;

    &::after {
      content: "";
      flex: 1;
      height: 1px;
      background: #e4e7ed;
      margin-left: 16px;
    }
  }

  .time-range {
    display: flex;
    align-items: center;

    span {
      color: #606266;
      font-size: 14px;
    }
  }

  .discount-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .form-text {
    margin: 0 10px;
    color: #606266;
    font-size: 14px;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    // gap: 12px;
    flex-shrink: 0;
    height: 60px;
    align-items: center;
    background-color: #fff;
    padding: 0 20px;
  }

  .form-item-group {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;

    .inline-form-item {
      margin-bottom: 0;
      margin-right: 0;
      :deep(.el-form-item__error) {
        left: 42px;
      }

      &:first-child {
        flex: 0 0 auto;
      }

      &:last-child {
        flex: 1;
        margin-left: 0;

        :deep(.el-form-item__label) {
          width: 0 !important;
          padding: 0 !important;
        }
      }
    }
  }

  .course-type-selection {
    width: 100%;
    max-width: 600px;

    :deep(.el-checkbox-group) {
      display: flex;
      flex-wrap: wrap;
      gap: 16px 24px;
    }

    .course-type-checkbox {
      margin-right: 0;
      margin-bottom: 0;

      :deep(.el-checkbox__label) {
        font-size: 14px;
        color: #606266;
        padding-left: 8px;
      }
    }
  }

  .organization-selection {
    width: 80%;
    // max-width: 800px;

    .search-bar {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;

      // 覆盖表单验证错误样式，防止搜索框显示红色边框
      :deep(.el-input) {
        .el-input__wrapper {
          box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset !important;

          &:hover {
            box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset !important;
          }

          &.is-focus {
            box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset !important;
          }
        }
      }
    }

    // 覆盖分页组件的表单验证错误样式
    :deep(.el-pagination) {
      display: flex;
      justify-content: center;

      .el-input {
        .el-input__wrapper {
          box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset !important;

          &:hover {
            box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset !important;
          }

          &.is-focus {
            box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset !important;
          }
        }
      }

      .el-select {
        .el-select__wrapper {
          box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset !important;

          &:hover {
            box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset !important;
          }

          &.is-focused {
            box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset !important;
          }
        }
      }
    }
  }
}
</style>
