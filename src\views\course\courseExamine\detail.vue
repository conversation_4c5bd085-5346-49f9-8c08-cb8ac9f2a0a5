<script setup>
import { ref, onMounted, nextTick } from "vue";
import { useRoute } from "vue-router";
import TabTitle from "@/components/Base/tabInfo.vue";
import BaseInfo from "./components/baseInfo.vue";
import Scheduling from "@/components/course/scheduling.vue";
import PriceSetting from "@/components/course/priceSetting.vue";
import courseIntroduction from "@/components/course/courseIntroduction.vue";
import JobDesign from "@/components/course/jobDesign.vue";
import Knowledge from "@/components/course/knowledge.vue";
import RejectDialog from "@/components/Base/rejectDialog.vue";
import {
  findByApplyId,
  reviewApproval,
  findcoursePeriodId,
  findBasicInformation,
  bureauFindBasicInformation,
  toTheLocalEnd,
  postponeReview
} from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { APPLY_STATE, APPROVAL_TYPE } from "@/utils/enum";
import { to } from "@iceywu/utils";
import { useUserStoreHook } from "@/store/modules/user";
import dayjs from "dayjs";
import { ImageThumbnail } from "@/utils/imageProxy.js";
import { localEndApplyReview, localEndFindByApplyId } from "@/api/localEnd.js";

const route = useRoute();
const textarea = ref("");
const infoShow = ref(true);
const tableShow = ref("基础信息");

// PriceSetting 组件引用
const priceSettingRef = ref(null);

// 审核类型
const currentApplyType = ref("");

// 费用相关字段
const serviceFeeRatio = ref("");
const serviceFee = ref("");
const insuranceFee = ref("");
const classHourFee = ref(0);
const materialFee = ref(0);

// 百分比转换辅助函数
const convertDecimalToPercentage = decimal => {
  if (decimal === null || decimal === undefined) return "";
  return decimal * 100;
};

const convertPercentageToDecimal = percentage => {
  if (!percentage || percentage === "") return null;
  return parseFloat(percentage) / 100;
};

// 失焦事件处理函数
const handleRatioBlur = () => {
  // 先处理小数位截断
  if (serviceFeeRatio.value !== "" && serviceFeeRatio.value !== null) {
    const num = parseFloat(serviceFeeRatio.value);
    if (!isNaN(num)) {
      const truncated = Math.floor(num * 100) / 100;
      const finalValue = truncated > 100 ? 100 : truncated;
      serviceFeeRatio.value = String(finalValue);
    }
  }

  const totalFee =
    (parseFloat(classHourFee.value) || 0) +
    (parseFloat(materialFee.value) || 0);
  if (totalFee === 0) return; // 总费用为0时不联动

  const ratio = parseFloat(serviceFeeRatio.value);
  if (!isNaN(ratio) && ratio >= 0 && ratio <= 100) {
    const calculatedFee = (totalFee * ratio) / 100;
    const truncatedFee = Math.floor(calculatedFee * 100) / 100;
    serviceFee.value = truncatedFee.toFixed(2);
  }
};

const handleFeeBlur = () => {
  // 先处理小数位截断
  if (serviceFee.value !== "" && serviceFee.value !== null) {
    const num = parseFloat(serviceFee.value);
    if (!isNaN(num)) {
      const truncated = Math.floor(num * 100) / 100;
      serviceFee.value = String(truncated);
    }
  }

  const totalFee =
    (parseFloat(classHourFee.value) || 0) +
    (parseFloat(materialFee.value) || 0);
  if (totalFee === 0) return; // 总费用为0时不联动

  const fee = parseFloat(serviceFee.value);
  if (!isNaN(fee) && fee >= 0) {
    // 检查是否存在当前比例值，如果存在则进行精度验证
    const currentRatio = parseFloat(serviceFeeRatio.value);
    if (!isNaN(currentRatio) && currentRatio >= 0 && currentRatio <= 100) {
      // 根据当前比例计算期望的服务费用
      const expectedFee = (totalFee * currentRatio) / 100;
      const truncatedExpectedFee = Math.floor(expectedFee * 100) / 100;

      // 计算差异，如果差异很小（小于0.02元，即2分），可能是浮点数精度问题
      const difference = Math.abs(fee - truncatedExpectedFee);
      const smallDifferenceTolerance = 0.02; // 2分的容差

      if (difference > 0 && difference <= smallDifferenceTolerance) {
        // 差异很小，可能是精度问题，纠正为期望值
        serviceFee.value = truncatedExpectedFee.toFixed(2);
        return; // 不需要重新计算比例
      }
    }

    // 正常的联动计算：根据费用计算比例
    const calculatedRatio = (fee / totalFee) * 100;
    const truncatedRatio = Math.floor(calculatedRatio * 100) / 100;

    // 如果计算出的比例超过100%，则限制比例为100%，并相应调整服务费
    if (truncatedRatio > 100) {
      serviceFeeRatio.value = "100";
      // 重新计算服务费，使其与100%比例对应
      const maxFee = totalFee * 1; // 100% = 1
      const truncatedMaxFee = Math.floor(maxFee * 100) / 100;
      serviceFee.value = truncatedMaxFee.toFixed(2);
    } else {
      serviceFeeRatio.value = truncatedRatio.toFixed(2);
    }
  }
};

// 保险费失焦处理
const handleInsuranceBlur = () => {
  if (insuranceFee.value !== "" && insuranceFee.value !== null) {
    if (/^0\.0\d*$/.test(insuranceFee.value)) {
      const num = parseFloat(insuranceFee.value);
      if (!isNaN(num)) {
        // 直接舍去小数点后两位后的数据
        const truncated = Math.floor(num * 100) / 100;
        insuranceFee.value = truncated.toFixed(2);
      }
      return;
    }

    let num = parseFloat(insuranceFee.value);
    if (!isNaN(num)) {
      // 确保不超过9999.99
      if (num > 9999.99) {
        num = 9999.99;
      }
      // 直接舍去小数点后两位后的数据
      const truncated = Math.floor(num * 100) / 100;
      insuranceFee.value = truncated.toFixed(2);
    }
  }
};

// 禁用负号输入
const handleKeydown = event => {
  // 禁用负号(-)和加号(+)
  if (event.key === "-" || event.key === "+") {
    event.preventDefault();
  }
};

// 输入验证函数
const validateNumberInput = (value, max = null) => {
  if (value === "" || value === null || value === undefined) return "";

  const num = parseFloat(value);
  if (isNaN(num)) return "";
  if (num < 0) return "";
  if (max !== null && num > max) return max;

  return value;
};

// 比例输入处理
const handleRatioInput = value => {
  serviceFeeRatio.value = validateNumberInput(value, 100);
};

// 服务费输入处理
const handleFeeInput = value => {
  serviceFee.value = validateNumberInput(value);
};

// 保险费输入处理
const handleInsuranceInput = value => {
  // 允许空值
  if (value === "" || value === null || value === undefined) {
    insuranceFee.value = value;
    return;
  }

  // 允许输入中的过渡状态（如 "1.", "0.0", "1.0" 等）
  if (
    value === "0." ||
    value === "0.0" ||
    /^0\.0\d*$/.test(value) ||
    /^\d+\.$/.test(value) ||
    /^\d+\.\d*$/.test(value)
  ) {
    // 检查是否超过最大值
    const num = parseFloat(value);
    if (!isNaN(num) && num > 9999.99) {
      insuranceFee.value = "9999.99";
      return;
    }
    insuranceFee.value = value;
    return;
  }

  // 其他情况使用数字验证
  const num = parseFloat(value);
  if (isNaN(num) || num < 0) {
    insuranceFee.value = "";
    return;
  }

  // 限制最大值为9999.99
  if (num > 9999.99) {
    insuranceFee.value = "9999.99";
    return;
  }

  insuranceFee.value = value;
};

// 驳回弹窗相关
const rejectDialogVisible = ref(false);

// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "课程名",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "期数",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "机构",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "申请时间",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "审核状态",
    value: "--",
    width: "107px"
  },
  {
    id: "6",
    label: "审核类型",
    value: "--",
    width: "107px"
  }
]);
const tableHeaderDetail = ref([
  {
    id: "1",
    label: "课程名",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "期数",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "机构",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "申请时间",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "审核状态",
    value: "--",
    width: "107px"
  },
  {
    id: "6",
    label: "审核类型",
    value: "--",
    width: "107px"
  },
  {
    id: "7",
    label: "审核员",
    value: "--",
    width: "107px"
  },
  {
    id: "8",
    label: "课程分类",
    value: "--",
    width: "107px"
  },
  {
    id: "9",
    label: "平台服务费比例",
    value: "--",
    width: "107px"
  },
  {
    id: "10",
    label: "平台服务费用",
    value: "--",
    width: "107px"
  },
  {
    id: "11",
    label: "保险费用",
    value: "--",
    width: "107px"
  }
]);
const tabTitle = ref([
  { id: 1, name: "基础信息" },
  { id: 2, name: "行程安排" },
  { id: 3, name: "课期介绍" },
  { id: 4, name: "课期知识点" },
  { id: 5, name: "材料说明" },
  { id: 6, name: "注意事项" },
  { id: 7, name: "价格设置" },
  { id: 8, name: "实践感悟" },
  { id: 9, name: "用户协议" }
]);
const baseInfo = ref([
  {
    id: "1",
    label: "课期ID",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "创建时间",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "人数上限",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "开课时间",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "实践点",
    value: "--",
    width: "107px"
  },

  {
    id: "6",
    label: "领队",
    value: "--",
    width: "107px"
  },
  {
    id: "7",
    label: "讲师",
    value: "--",
    width: "107px"
  }
  // {
  //   id: "8",
  //   label: "讲师",
  //   value: "--",
  //   width: "107px"
  // }

  // 其他表头数据项
]);
const baseInfoNo = ref([]);
const noLoading = ref(false);
const passLoading = ref(false);
const tabInfoEvt = obj => {
  // console.log("💗tabInfoEvt---------->", obj);
  tableShow.value = obj.name;
};
// 不通过
const nopassEvt = text => {
  rejectDialogVisible.value = true;
};

// 通过
const showLocalEnd = ref(useUserStoreHook().roleTarget === "局端管理员");
const showLocalEndDialod = ref(false);
const passEvt = text => {
  passLoading.value = true;
  showLocalEndDialod.value = true;
  passLoading.value = false;
};
// 转交到局端
const getLocalEndPass = async () => {
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `转交了"${tableHeader.value[0].value}"课期的${tableHeader.value[5].value}申请到局端`
  };
  const paramsArg = {
    id: Number(route.query.id),
    serviceFeeRatio: parseFloat(serviceFeeRatio.value) / 100,
    serviceFee: Number(serviceFee.value),
    insuranceFee: Number(insuranceFee.value)
  };
  try {
    const data = await toTheLocalEnd(paramsArg, operateLog);
    if (data.code === 200) {
      ElMessage.success("已转交局端审核");
      infoShow.value = !infoShow.value;
      getFindByApplyId();
    }
  } catch (err) {
    ElMessage.error("转交局端审核失败，请稍后重试");
  }
  showLocalEndDialod.value = false;
};

// 确认通过
const getPassEvt = async text => {
  try {
    // 根据审核类型选择不同的接口
    if (currentApplyType.value === "COURSE_PERIOD_POSTPONE") {
      // 改期审核使用postponeReview接口
      handlePostponeReview("APPROVED");
    } else {
      // 其他审核类型使用reviewApproval接口
      getReviewApproval(text);
    }
  } catch (error) {
    ElMessage.error("审核失败，请稍后重试");
    return;
  }
  showLocalEndDialod.value = false;
};

// 处理驳回确认事件
const handleRejectConfirm = data => {
  noLoading.value = true;

  // 根据审核类型选择不同的接口
  if (currentApplyType.value === "COURSE_PERIOD_POSTPONE") {
    // 改期审核使用postponeReview接口
    handlePostponeReview("REJECTED", data.reason);
  } else {
    // 其他审核类型使用reviewApproval接口
    getReviewApproval("REJECTED", data.reason);
  }

  rejectDialogVisible.value = false;
};

// 处理改期审核
const handlePostponeReview = async (auditState, opinion) => {
  const paramsArg = {
    applyId: Number(route.query.id),
    auditState,
    userType:
      useUserStoreHook().roleTarget === "局端管理员"
        ? "EDUCATION_BUREAU"
        : "PLATFORM_ADMIN"
  };

  // 添加审核意见
  if (opinion) {
    paramsArg.opinion = opinion;
  } else if (textarea.value) {
    paramsArg.opinion = textarea.value;
  }

  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType:
      auditState === "REJECTED"
        ? `驳回了"${tableHeader.value[0].value}"课期的${tableHeader.value[5].value}申请`
        : `通过了"${tableHeader.value[0].value}"课期的${tableHeader.value[5].value}申请`
  };

  try {
    const [err, res] = await to(postponeReview(paramsArg, operateLog));
    if (res && res.code === 200) {
      ElMessage.success("审核成功");
      infoShow.value = !infoShow.value;
      getFindByApplyId();
    } else {
      ElMessage.error(`审核失败,${res?.msg || "请稍后重试"}`);
    }
  } catch (error) {
    ElMessage.error("审核失败，请稍后重试");
  } finally {
    noLoading.value = false;
  }
};

// 审核
const getReviewApproval = async (text, opinion) => {
  const paramsArg = {
    applyId: Number(route.query.id),
    auditState: text,
    userType:
      useUserStoreHook().roleTarget === "局端管理员"
        ? "EDUCATION_BUREAU"
        : "PLATFORM_ADMIN"
  };

  // 处理驳回理由，使用opinion参数
  if (text === "REJECTED" && opinion) {
    paramsArg.opinion = opinion;
  } else if (textarea.value) {
    paramsArg.opinion = textarea.value;
  }

  // 添加费用相关参数
  if (text === "APPROVED") {
    // serviceFeeRatio需要转换为小数
    if (
      serviceFeeRatio.value !== "" &&
      serviceFeeRatio.value !== null &&
      !isNaN(parseFloat(serviceFeeRatio.value))
    ) {
      paramsArg.serviceFeeRatio = parseFloat(serviceFeeRatio.value) / 100;
    }
    // serviceFee 和 insuranceFee 直接使用，因为它们是用户输入的值
    if (
      serviceFee.value !== "" &&
      serviceFee.value !== null &&
      !isNaN(parseFloat(serviceFee.value))
    ) {
      paramsArg.serviceFee = parseFloat(serviceFee.value);
    }
    if (
      insuranceFee.value !== "" &&
      insuranceFee.value !== null &&
      !isNaN(parseFloat(insuranceFee.value))
    ) {
      paramsArg.insuranceFee = parseFloat(insuranceFee.value);
    }
    // materialFee 不需要传递给后端，因为它只用于前端计算
  }

  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType:
      text === "REJECTED"
        ? `驳回了"${tableHeader.value[0].value}"课期的${tableHeader.value[5].value}申请`
        : `通过了"${tableHeader.value[0].value}"课期的${tableHeader.value[5].value}申请`
  };

  let api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? localEndApplyReview
      : reviewApproval;
  const [err, res] = await to(api(paramsArg, operateLog));
  if (res.code === 200) {
    ElMessage.success("审核成功");
    infoShow.value = !infoShow.value;
    getFindByApplyId();
    // 刷新价格设置数据
    if (priceSettingRef.value && priceSettingRef.value.refreshData) {
      priceSettingRef.value.refreshData();
    }
    // 清空驳回理由
    selectedReasons.value = [];
    customReason.value = "";
    // 清空费用字段
    classHourFee.value = 0;
    materialFee.value = 0;
    serviceFeeRatio.value = "";
    serviceFee.value = "";
    insuranceFee.value = "";
  } else {
    ElMessage.error(`审核失败,${res.msg}`);
  }
  if (err) {
    ElMessage.error("审核失败");
  }
  noLoading.value = false;
};
const params = ref({
  page: 1,
  size: 10,
  sort: "createdAt,desc",
  totalElements: 0
});
// 查询详情
let tableImg = ref("");
const getFindByApplyId = async () => {
  const paramsArg = {
    applyId: route.query.id
  };
  const api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? localEndFindByApplyId
      : findByApplyId;
  const [err, res] = await requestTo(api(paramsArg));
  // console.log("🦄-----err, res-----", err, res);
  if (res) {
    // console.log("🐬res------------------------------>", res);
    if (
      res.auditState === "PENDING_REVIEW" ||
      (res.auditState === "EDUCATION_BUREAU_PENDING_REVIEW" &&
        showLocalEnd.value)
    ) {
      infoShow.value = false;
      tableHeader.value[0].value = res.coursePeriodName || "--";
      tableHeader.value[1].value = res.termNumber || "--";
      tableHeader.value[2].value = res.organizationName || "--";
      tableHeader.value[3].value = res.createdAt
        ? dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss")
        : "--";
      tableHeader.value[4].value = APPLY_STATE[res.auditState]?.text || "--";
      tableHeader.value[5].value = APPROVAL_TYPE[res.applyType]?.text || "--";

      // 设置当前审核类型
      currentApplyType.value = res.applyType;
      if (res.applyType === "COURSE_PERIOD_OFFLINE") {
        tableHeader.value[6] = {
          id: "7",
          label: "下架申请理由",
          value: res.reason || "--",
          width: "107px"
        };
      } else if (res.applyType === "COURSE_PERIOD_POSTPONE") {
        tableHeader.value[6] = {
          id: "7",
          label: "改期时间",
          value: res.postponeOpenTime
            ? dayjs(res.postponeOpenTime).format("YYYY-MM-DD")
            : "--",
          width: "107px"
        };
        tableHeader.value[7] = {
          id: "8",
          label: "原开课时间",
          value: res.originTime
            ? dayjs(res.originTime).format("YYYY-MM-DD")
            : "--",
          width: "107px"
        };
        tableHeader.value[8] = {
          id: "9",
          label: "改期理由",
          value: res.reason || "--",
          width: "107px"
        };
      }
      // 回显费用字段
      classHourFee.value =
        res.classHourFee !== null && res.classHourFee !== undefined
          ? res.classHourFee
          : 0;
      materialFee.value =
        res.materialFee !== null && res.materialFee !== undefined
          ? res.materialFee
          : 0;
      serviceFeeRatio.value =
        res.serviceFeeRatio !== null && res.serviceFeeRatio !== undefined
          ? (res.serviceFeeRatio * 100).toFixed(2)
          : "";
      // 处理serviceFee回显逻辑
      if (res.serviceFee !== null && res.serviceFee !== undefined) {
        // 如果serviceFee有值，直接使用
        serviceFee.value = res.serviceFee.toFixed(2);
      } else if (
        res.serviceFeeRatio !== null &&
        res.serviceFeeRatio !== undefined &&
        ((res.classHourFee !== null && res.classHourFee !== undefined) ||
          (res.materialFee !== null && res.materialFee !== undefined))
      ) {
        // 如果serviceFee为null但有serviceFeeRatio和classHourFee/materialFee，则计算serviceFee
        const totalBaseFee = (res.classHourFee || 0) + (res.materialFee || 0);
        const calculatedFee = totalBaseFee * res.serviceFeeRatio;
        const truncatedFee = Math.floor(calculatedFee * 100) / 100;
        serviceFee.value = truncatedFee.toFixed(2);
      } else {
        serviceFee.value = "";
      }
      insuranceFee.value =
        res.insuranceFee !== null && res.insuranceFee !== undefined
          ? res.insuranceFee.toString()
          : "";
    } else {
      infoShow.value = true;

      // 设置当前审核类型
      currentApplyType.value = res.applyType;
      tableHeaderDetail.value[0].value = res.coursePeriodName || "--";
      tableHeaderDetail.value[1].value = res.termNumber || "--";
      tableHeaderDetail.value[2].value = res.organizationName || "--";
      tableHeaderDetail.value[3].value = res.createdAt
        ? dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss")
        : "--";
      tableHeaderDetail.value[4].value =
        APPLY_STATE[res.auditState]?.text || "--";
      tableHeaderDetail.value[5].value =
        APPROVAL_TYPE[res.applyType]?.text || "--";
      tableHeaderDetail.value[6].value = res.auditorName || "--";
      tableImg.value = res.files ? res.files[0]?.uploadFile?.url : "";
      tableHeaderDetail.value[7].value = res?.courseType || "--";
      // 设置费用相关字段
      tableHeaderDetail.value[8].value =
        res.serviceFeeRatio !== null && res.serviceFeeRatio !== undefined
          ? res.serviceFeeRatio * 100 + "%"
          : "--";
      tableHeaderDetail.value[9].value =
        res.serviceFee !== null && res.serviceFee !== undefined
          ? res.serviceFee + "元"
          : "--";
      tableHeaderDetail.value[10].value =
        res.insuranceFee !== null && res.insuranceFee !== undefined
          ? res.insuranceFee + "元"
          : "--";

      if (res.applyType === "COURSE_PERIOD_OFFLINE") {
        tableHeaderDetail.value[11] = {
          id: "12",
          label: "下架申请理由",
          value: res.reason || "--",
          width: "107px"
        };
        tableHeaderDetail.value[12] = {
          id: "13",
          label: "审核意见",
          value: res.opinion || "--",
          width: "107px"
        };
      } else if (res.applyType === "COURSE_PERIOD_POSTPONE") {
        tableHeaderDetail.value[11] = {
          id: "12",
          label: "改期时间",
          value: res.postponeOpenTime
            ? dayjs(res.postponeOpenTime).format("YYYY-MM-DD")
            : "--",
          width: "107px"
        };
        tableHeaderDetail.value[12] = {
          id: "13",
          label: "原开课时间",
          value: res.originTime
            ? dayjs(res.originTime).format("YYYY-MM-DD")
            : "--",
          width: "107px"
        };
        tableHeaderDetail.value[13] = {
          id: "14",
          label: "改期理由",
          value: res.reason || "--",
          width: "107px"
        };
        tableHeaderDetail.value[14] = {
          id: "15",
          label: "审核意见",
          value: res.opinion || "--",
          width: "107px"
        };
      } else {
        tableHeaderDetail.value[11] = {
          id: "12",
          label: "审核意见",
          value: res.opinion || "--",
          width: "107px"
        };
      }
    }

    // console.log('🌳 tableData.value------------------------------>',tableData.value);
  }
  if (err) {
    console.log("err------------------------------>", err);
  }
};
// 查询基础信息
const getCoursePeriodId = async () => {
  const paramsArg = {
    coursePeriodId: route.query.periodId
  };
  let api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? bureauFindBasicInformation
      : findBasicInformation;
  const [err, res] = await requestTo(api(paramsArg));
  // console.log("🌈-----err, res-----", err, res);
  if (res) {
    // console.log("🐬res---------------3333---3333222------------>", res);
    baseInfo.value[0].value = res?.id || "--";
    baseInfo.value[1].value = res.createdAt
      ? dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss")
      : "--";
    baseInfo.value[2].value = res?.maxPeopleNumber || "--";
    baseInfo.value[3].value = res.openTime
      ? dayjs(res.openTime).format("YYYY-MM-DD HH:mm:ss")
      : "--";
    baseInfo.value[4].value = res?.complex?.name || 0;
    baseInfo.value[5].value =
      res?.leaders?.map(it => it.name).join("、") || "--";
    baseInfo.value[6].value =
      res?.lecturers?.map(it => it.name).join("、") || "--";

    // console.log('🌳 tableData.value------------------------------>',tableData.value);
  }
  if (err) {
    console.log("err------------------------------>", err);
  }
};
onMounted(() => {
  getFindByApplyId();
  getCoursePeriodId();
});
</script>

<template>
  <div class="examine-detail">
    <div class="curse-table">
      <!-- 详情 -->
      <div v-if="infoShow" class="examine-info">
        <el-descriptions class="margin-top" title="" :column="3" border>
          <el-descriptions-item
            :rowspan="4"
            :width="140"
            label="课程封面"
            align="center"
          >
            <el-image v-if="tableImg" :src="ImageThumbnail(tableImg)" />
            <span v-else>暂无封面</span>
          </el-descriptions-item>
          <template v-for="(item, index) in tableHeaderDetail" :key="index">
            <el-descriptions-item
              v-if="
                !(
                  item.label === '审核意见' &&
                  tableHeaderDetail[4].value === '已通过'
                ) &&
                !(
                  (currentApplyType === 'COURSE_PERIOD_OFFLINE' &&
                    (item.label === '平台服务费比例' ||
                      item.label === '平台服务费用' ||
                      item.label === '保险费用')) ||
                  (currentApplyType === 'COURSE_PERIOD_POSTPONE' &&
                    (item.label === '平台服务费比例' ||
                      item.label === '平台服务费用' ||
                      item.label === '保险费用'))
                )
              "
              width="120px"
              label-align="center"
              :span="
                item.label === '审核意见' || item.label === '下架申请理由'
                  ? 3
                  : ''
              "
            >
              <template #label>
                <div class="cell-item">{{ item.label }}</div>
              </template>
              <div
                :style="{
                  color:
                    item.value === '已驳回'
                      ? '#FF374C'
                      : item.value === '已通过'
                        ? '#409EFF'
                        : item.value === '局端待审批'
                          ? '#FF9C41'
                          : ''
                }"
              >
                {{ item.value }}
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>
      <!-- 审核中 -->
      <div v-else class="examine-pending">
        <div class="pending-container">
          <div class="pengding-info">
            <el-descriptions class="margin-top" title="" :column="3" border>
              <template v-for="(item, index) in tableHeader" :key="index">
                <el-descriptions-item width="120px" label-align="center">
                  <template #label>
                    <div class="cell-item">{{ item.label }}</div>
                  </template>
                  <div
                    :style="{
                      color:
                        item.value === '待审批'
                          ? '#FF9C41'
                          : item.value === '局端待审批'
                            ? '#FF9C41'
                            : ''
                    }"
                  >
                    {{ item.value }}
                  </div>
                </el-descriptions-item>
              </template>
            </el-descriptions>

            <!-- 费用设置 -->
            <div
              v-if="
                currentApplyType !== 'COURSE_PERIOD_OFFLINE' &&
                currentApplyType !== 'COURSE_PERIOD_POSTPONE' &&
                !showLocalEnd
              "
              class="fee-settings"
            >
              <div class="fee-row">
                <div class="fee-item">
                  <label class="fee-label">平台服务费比例</label>
                  <el-input
                    v-model="serviceFeeRatio"
                    type="number"
                    :min="0"
                    :max="100"
                    step="0.01"
                    placeholder="5"
                    class="fee-input"
                    @blur="handleRatioBlur"
                    @keydown="handleKeydown"
                    @input="handleRatioInput"
                  />
                  <span class="fee-unit">%</span>
                </div>
                <div class="fee-item">
                  <label class="fee-label">平台服务费用</label>
                  <el-input
                    v-model="serviceFee"
                    type="number"
                    :min="0"
                    step="0.01"
                    placeholder="5"
                    class="fee-input"
                    @blur="handleFeeBlur"
                    @keydown="handleKeydown"
                    @input="handleFeeInput"
                  />
                  <span class="fee-unit">元</span>
                </div>
                <div class="fee-item">
                  <label class="fee-label">保险费用</label>
                  <el-input
                    v-model="insuranceFee"
                    type="number"
                    :min="0"
                    step="0.01"
                    placeholder="请输入"
                    class="fee-input"
                    @keydown="handleKeydown"
                    @input="handleInsuranceInput"
                    @blur="handleInsuranceBlur"
                  />
                  <span class="fee-unit">元</span>
                </div>
              </div>
            </div>
          </div>
          <div class="opinion-btn">
            <el-button
              type="primary"
              style="margin-bottom: 10px"
              :loading="passLoading"
              @click="passEvt('APPROVED')"
            >
              通过
            </el-button>
            <el-button
              type="danger"
              :loading="noLoading"
              @click="nopassEvt('REJECTED')"
            >
              驳回
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 驳回理由弹窗 -->
    <RejectDialog
      v-model:dialogFormVisible="rejectDialogVisible"
      :apply-type="currentApplyType"
      @confirm="handleRejectConfirm"
      @reset="rejectDialogVisible = false"
    />

    <!-- 通过申请弹窗 -->
    <el-dialog v-model="showLocalEndDialod" title="确定通过" width="400">
      <div>确定要通过该申请吗？</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            v-if="
              !showLocalEnd &&
              tableHeader[tableHeader.length - 1].value === '上架'
            "
            type="primary"
            plain
            @click="getLocalEndPass"
          >
            转交局端审核
          </el-button>
          <el-button v-else @click="showLocalEndDialod = false">
            取消
          </el-button>
          <el-button type="primary" @click="getPassEvt('APPROVED')">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <div class="info-table">
      <!-- tab切换 -->
      <TabTitle :tabTitle="tabTitle" @tab-data="tabInfoEvt" />
      <!-- 切换信息 -->
      <div class="tab-info">
        <BaseInfo
          v-if="tableShow === '基础信息'"
          :baseInfo="baseInfo"
          :baseInfoNo="baseInfoNo"
          :type="route.query.type"
        />
        <Scheduling
          v-if="tableShow === '行程安排'"
          :periodId="Number(route.query.periodId)"
        />
        <PriceSetting
          v-if="tableShow === '价格设置'"
          ref="priceSettingRef"
          :periodId="Number(route.query.periodId)"
        />
        <courseIntroduction
          v-if="
            tableShow === '课期介绍' ||
            tableShow === '材料说明' ||
            tableShow === '注意事项' ||
            tableShow === '用户协议'
          "
          :periodId="Number(route.query.periodId)"
          :tableTitle="tableShow"
        />
        <Knowledge
          v-else-if="tableShow === '课期知识点'"
          :periodId="Number(route.query.periodId)"
        />
        <JobDesign
          v-if="tableShow === '实践感悟'"
          :periodId="Number(route.query.periodId)"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.examine-detail {
  height: 88vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .curse-table {
    box-sizing: border-box;
    width: 100%;
    padding: 24px 20px;
    margin-bottom: 30px;
    background-color: #fff;
    flex-shrink: 0; // 防止压缩

    .examine-pending {
      .pending-container {
        display: flex;
        height: 100%;
        align-items: flex-end; // 垂直底部对齐
        justify-content: flex-end; // 水平右侧对齐
        gap: 20px;

        .pengding-info {
          flex: 1;
        }

        .opinion-btn {
          display: flex;
          flex-direction: column;
          align-items: flex-end; // 按钮在容器中右对齐
          min-width: 80px;
        }
      }
    }

    // 费用设置样式
    .fee-settings {
      margin-top: 20px;
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 4px;
      border: 1px solid #e9ecef;

      .fee-row {
        display: flex;
        gap: 20px;
        align-items: center;

        .fee-item {
          display: flex;
          align-items: center;
          gap: 8px;

          .fee-label {
            font-size: 14px;
            color: #333;
            white-space: nowrap;
            min-width: 100px;
          }

          .fee-input {
            width: 80px;
          }

          .fee-unit {
            font-size: 14px;
            color: #666;
            min-width: 20px;
          }
        }
      }
    }
  }

  .info-table {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .tab-info {
      flex: 1;
      box-sizing: border-box;
      width: 100%;
      padding: 20px 20px;
      background-color: #fff;
      overflow-y: auto; // 内容过多时可滚动
    }
  }
}

// 驳回理由弹窗样式
.reject-reason-container {
  .reason-tags {
    margin-bottom: 20px;

    .reason-tags-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 10px;
      color: #333;
    }

    .reason-tags-content {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .reason-tag {
      transition: all 0.3s;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .custom-reason {
    .custom-reason-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 10px;
      color: #333;
    }
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
</style>
