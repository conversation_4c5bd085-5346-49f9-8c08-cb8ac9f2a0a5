<script setup>
import { ref, reactive, onMounted } from "vue";
import PureTable from "@pureadmin/table";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { findOrganizationByCouponId } from "@/api/coupon";
import { requestTo } from "@/utils/http/tool";

defineOptions({
  name: "OrganizationList"
});

// 机构列表和分页
const organizationList = ref([]);
const organizationTableRef = ref();
const organizationPagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

const organizationLoading = ref(false);
const organizationSearchName = ref("");
const organizationSearchAlias = ref("");

const route = useRoute();
const couponId = route.query.id;

// 机构表格列配置
const organizationColumns = [
  {
    label: "机构名称",
    prop: "name",
    minWidth: 200,
    formatter: ({ name }) => name || "--"
  },
  {
    label: "机构别名",
    prop: "alias",
    minWidth: 150,
    formatter: ({ alias }) => alias || "--"
  },
  {
    label: "机构ID",
    prop: "id",
    width: 100,
    align: "center"
  }
];

// 获取机构列表
const getOrganizationList = async (resetPage = false) => {
  if (resetPage) {
    organizationPagination.value.currentPage = 1;
  }

  organizationLoading.value = true;
  try {
    const params = {
        couponId,
      page: organizationPagination.value.currentPage - 1, // 后端从0开始
      size: organizationPagination.value.pageSize,
    };

    // 添加搜索条件
    if (organizationSearchName.value.trim()) {
      params.organizationName = organizationSearchName.value.trim();
    }
    if (organizationSearchAlias.value.trim()) {
      params.alias = organizationSearchAlias.value.trim();
    }

    const [err, result] = await requestTo(findOrganizationByCouponId(params));
    if (result && result.content) {
      organizationList.value = result.content;
      organizationPagination.value.total = result.totalElements || 0;
    } else if (err) {
      ElMessage.error(err.message || "获取机构列表失败");
    }
  } catch (error) {
    console.error("获取机构列表失败:", error);
    ElMessage.error("获取机构列表失败，请重试");
  } finally {
    organizationLoading.value = false;
  }
};

// 处理机构搜索
const handleOrganizationSearch = () => {
  getOrganizationList(true);
};

// 重置机构搜索
const handleOrganizationReset = () => {
  organizationSearchName.value = "";
  organizationSearchAlias.value = "";
  getOrganizationList(true);
};

// 处理机构分页变化
const handleOrganizationPageChange = (page) => {
  organizationPagination.value.currentPage = page;
  getOrganizationList();
};

// 处理机构每页大小变化
const handleOrganizationSizeChange = (size) => {
  organizationPagination.value.pageSize = size;
  getOrganizationList(true);
};

onMounted(() => {
  getOrganizationList();
});
</script>

<template>
  <div class="organization-list-container">
    <el-form :inline="true" class="search-form">
      <el-form-item label="机构名称">
        <el-input v-model="organizationSearchName" placeholder="请输入机构名称" clearable />
      </el-form-item>
      <el-form-item label="机构别名">
        <el-input v-model="organizationSearchAlias" placeholder="请输入机构别名" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleOrganizationSearch">搜索</el-button>
        <el-button @click="handleOrganizationReset">重置</el-button>
      </el-form-item>
    </el-form>

    <pure-table
      ref="organizationTableRef"
      row-key="id"
      adaptive
      :adaptiveConfig="{ offsetBottom: 120 }"
      align-whole="left"
      table-layout="auto"
      :loading="organizationLoading"
      :data="organizationList"
      :columns="organizationColumns"
      :pagination="organizationPagination"
      :header-cell-style="{
        background: 'var(--el-fill-color-light)',
        color: 'var(--el-text-color-primary)'
      }"
      @page-size-change="handleOrganizationSizeChange"
      @page-current-change="handleOrganizationPageChange"
    />
</div>
</template>

<style lang="scss" scoped>
.organization-list-container {
  height: 88vh;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: var(--el-bg-color);

  .search-form {
    // flex: 1;
    padding: 20px;
  }

}
:deep(.el-pagination) {
  .el-pagination__sizes {
    margin-right: 15px;
  }
  .el-pager li {
    background-color: #f0f2f5;
    border-radius: 2px;
    margin: 0 4px;
  }

  .el-pager li.is-active {
    background-color: #409eff;
    color: #fff;
    border-color: #409eff;
  }
  .el-pagination__jump,
  .el-pagination__sizes {
    .el-input__inner {
      background-color: #fff;
    }
  }
  .btn-prev,
  .btn-next {
    background-color: #f0f2f5;
    border-radius: 2px;
    margin: 0 4px;
  }
  .btn-prev:hover,
  .btn-next:hover {
    color: #409eff;
  }
}
</style>
