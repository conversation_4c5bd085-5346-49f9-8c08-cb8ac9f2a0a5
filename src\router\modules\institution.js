import { $t } from "@/plugins/i18n";
import { institution } from "@/router/enums.js";
import { inCodesList, reCodesList } from "@/router/accidCode.js";
import InstitutionIcon from "@/assets/home/<USER>";
import InstitutionIconActive from "@/assets/home/<USER>";
export default {
  path: "/institution",
  redirect: "/institution/institutionManage",
  meta: {
    icon: "ri:information-line",
    imgIcon: InstitutionIcon,
    imgIconActive: InstitutionIconActive,
    // showLink: false,
    title: "机构",
    rank: institution,
    idCode: inCodesList.baseCode
  },
  children: [
    {
      path: "/institution/institution",
      name: "institution",
      redirect: "/institution/institutionManage",
      // component: () => import("@/views/institution/institutionManage.vue"),
      meta: {
        title: "机构管理"
        // idCode: inCodesList.organizationalManagement
      },
      children: [
        {
          path: "/institution/institutionManage",
          name: "InstitutionManage",
          component: () => import("@/views/institution/institutionManage.vue"),
          meta: {
            title: "机构管理",
            idCode: inCodesList.organizationalManagement,
            keepAlive: true
          }
        },
        {
          path: "/institution/baseAdd",
          name: "baseAdd",
          component: () => import("@/views/institution/baseAdd.vue"),
          meta: {
            title: "机构-新建机构",
            idCode: reCodesList.baseAdd,
            showLink: false
          }
        },
        {
          path: "/institution/baseEdit",
          name: "baseEdit",
          component: () => import("@/views/institution/baseEdit.vue"),
          meta: {
            title: "机构-机构编辑",
            idCode: reCodesList.baseEdit,
            showLink: false
          }
        },
        {
          path: "/institution/accounting",
          name: "accounting",
          // redirect: "/institution/accounting",
          component: () => import("@/views/institution/accounting.vue"),
          meta: {
            title: "机构-财务",
            idCode: reCodesList.accounting,
            showLink: false
          },
          children: [
            {
              path: "/institution/orderDetails",
              name: "orderDetails",
              component: () => import("@/views/institution/orderDetails.vue"),
              meta: {
                title: "订单详情",
                idCode: reCodesList.orderDetails,
                showLink: false
              },
              children: [
                {
                  path: "/institution/management/current",
                  name: "institutionManagementCurrent",
                  component: () =>
                    import(
                      "@/views/course/courseManagement/currentDetails.vue"
                    ),
                  meta: {
                    // title: $t("menus.pureFourZeroOne")
                    title: "当期课程详情",
                    idCode: reCodesList.courseManagementCurrentDetails,
                    showLink: false
                  }
                }
              ]
            },
            {
              path: "/institution/management/orderDetails",
              name: "institutionManagementOrderDetails",
              component: () =>
                import("@/views/course/courseManagement/currentDetails.vue"),
              meta: {
                // title: $t("menus.pureFourZeroOne")
                title: "当期详情",
                // idCode: reCodesList.courseManagementCurrentDetails,
                showLink: false
              }
            },
            {
              path: "/institution/accounting/bankAccount",
              name: "bankAccount",
              component: () => import("@/views/institution/bankAccount.vue"),
              meta: {
                title: "银行账户信息",
                idCode: reCodesList.bankAccount,
                showLink: false
              }
            }
          ]
        }
      ]
    },
    {
      path: "/institution/institutionExamine",
      name: "机构审核",
      component: () => import("@/views/institution/institutionExamine.vue"),
      meta: {
        title: "机构审核",
        idCode: inCodesList.institutionalReview
      }
    },
    {
      path: "/institution/base",
      name: "base",
      redirect: "/institution/baseManage",
      // component: () => import("@/views/institution/baseManage.vue"),
      meta: {
        title: "实践点管理",
        idCode: inCodesList.baseManagement
      },
      children: [
        {
          path: "/institution/baseManage",
          name: "BaseManage",
          component: () => import("@/views/institution/baseManage.vue"),
          meta: {
            title: "实践点管理",
            idCode: inCodesList.baseManagement,
            keepAlive: true
          }
        },
        {
          path: "/institution/baseDetails",
          name: "baseDetails",
          component: () => import("@/views/institution/baseDetails.vue"),
          meta: {
            title: "实践点管理-详情",
            idCode: reCodesList.baseDetails,
            showLink: false
          }
        }
      ]
    },
    {
      path: "/institution/baseExamine",
      name: "实践点审核",
      component: () => import("@/views/institution/baseExamine.vue"),
      meta: {
        title: "实践点审核",
        idCode: inCodesList.baseAudit
      }
    }
  ]
};
