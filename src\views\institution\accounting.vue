<script setup>
import { onMounted, ref, nextTick } from "vue";
import { formatTime, downloadFileBySaveAs } from "@/utils/index";
import {
  findOrganizationFinancial,
  financialRecordFindAll,
  financialRecordExport
} from "@/api/institution";
import { ElLoading, ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { getAsyncTask } from "@/utils/common";

onMounted(() => {
  Id.value = route.query.id;
  organizationName.value = route.query.name;
  console.log("🌳-----Id.value-----", Id.value);
  getFinancial();
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
  // courseTypeFindApi();
});

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 400px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去表单高度、页面其他元素高度和边距
    tableHeight.value = `calc(100vh - 260px - ${searchFormHeight.value}px - 106px)`;
  }
};

const Id = ref(0);
const organizationName = ref("");
const router = useRouter();
const route = useRoute();
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  organizationName: "",
  recordType: "",
  channelType: ""
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "transactionTime,desc",
  totalElements: 0
});
const courseTypeoptions = ref([
  {
    value: "",
    label: "全部"
  },
  {
    value: "INCOME",
    label: "收入"
    // type: "INCOME"
  },
  {
    value: "EXPENDITURE",
    label: "支出"
    // type: "EXPENDITURE"
  }
]);
const channelTypeoptions = ref([
  {
    value: "",
    label: "全部"
  },
  {
    value: "WECHAT",
    label: "微信支付"
    // type: "WECHAT"
  },
  {
    value: "ALPAY",
    label: "支付宝支付"
    // type: "ALPAY"
  },
  {
    value: "PUBLIC_ACCOUNT",
    label: "公账"
    // type: "PUBLIC_ACCOUNT"
  },
  {
    value: "PLATFORM",
    label: "平台"
    // type: "PLATFORM"
  }
]);
const feeTypeoptions = ref([
  {
    value: "",
    label: "全部"
  },
  {
    value: "CLASS_HOUR",
    label: "课时费"
  },
  {
    value: "INSURANCE",
    label: "保险费"
  },
  {
    value: "MATERIAL",
    label: "材料费"
  },
  {
    value: "SERVICE",
    label: "服务费"
  }
]);
const financialData = ref({});
const getFinancial = async () => {
  const params = {
    organizationId: Id.value
  };
  console.log("🐳-----params-----", params);
  try {
    const { code, data, msg } = await findOrganizationFinancial(params);
    financialData.value = data;
    console.log("🎉-----code, data, msg-----", code, data, msg);
  } catch (error) {
    console.log("🚨-----error-----", error);
  }
};
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async item => {
  if (getListLoading.value) return;
  // return;
  getListLoading.value = true;
  let paramsData = {
    organizationId: Id.value,
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  // Only include supported API parameters
  const supportedParams = [
    "startTime",
    "endTime",
    "recordType",
    "channelType",
    "feeType"
  ];
  for (const key of supportedParams) {
    if (form.value[key]) {
      paramsData[key] = form.value[key];
    }
  }
  console.log("🍧-----paramsData-----", paramsData);
  const { code, data, msg } = await financialRecordFindAll(paramsData);
  console.log("🎁-----data-----", data);
  if (data) {
    tableData.value = data?.content;

    params.value.totalElements = data.totalElements;
    await nextTick();
    calculateTableHeight();
  }
  getListLoading.value = false;
};
// 处理每页数量变化
const handleSizeChange = val => {
  params.value.size = val;
  getTableList();
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
// 编辑
const edit = ordersId => {
  // sessionStorage.setItem("orderDetailsData", ordersId);
  router.push({
    path: "/institution/orderDetails",
    query: { id: ordersId }
  });
};
const getId = coursePeriodId => {
  router.push({
    path: "/institution/management/orderDetails",
    query: {
      periodId: coursePeriodId,
      text: "course"
    }
  });
};
const goSet = () => {
  router.push({
    path: "/institution/accounting/bankAccount",
    query: { id: Id.value, name: organizationName.value }
  });
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {};
  params.value.page = 1;
  value1.value = [];
};
// 选择时间
const timeChange = async value => {
  if (!value || value.length !== 2) return;

  // 开始时间（默认 00:00:00）
  form.value.startTime = new Date(value[0]).getTime();

  // 结束时间（设置为 23:59:59.999）
  const endDate = new Date(value[1]);
  endDate.setHours(23, 59, 59, 999); // 关键修改
  form.value.endTime = endDate.getTime();
  await nextTick();
  calculateTableHeight();
};

const value1 = ref([]);
const exportLoading = ref(false);
// 导出
const ProjectSubmission = async () => {
  if (exportLoading.value) return;
  exportLoading.value = true;
  // const loadingInstance = ElLoading.service({
  //   lock: true,
  //   text: "导出中...",
  //   background: "rgba(0, 0, 0, 0.7)"
  // });
  let paramsData = {
    organizationId: Id.value
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  console.log("🦄-----paramsData-----", paramsData);
  let { code, data } = await financialRecordExport(paramsData);
  console.log(data, "导出文件");
  // return;
  if (code == 200 && data?.id) {
    const task = await getAsyncTask(data?.id);
    console.log("🐬-----code, data-----", task.code, task.data);
    if (task.data.complete && task.data.success) {
      let resData = {};
      if (task.data.res) {
        resData = JSON.parse(task.data.res);
        console.log("🍪-----data-----", resData.url);
        downloadFileBySaveAs(resData.url, resData.fileName);
      }
      ElMessage.success("导出成功");
    } else {
      ElMessage.error(task.data.errMsg);
    }
    // const { id, complete, success } = data;
    // console.log("data: ", data);
    // console.log("导出结果", id, complete, success);
    // const { pause, resume, isActive } = useIntervalFn(() => {
    //   let params = {
    //     id
    //   };
    //   console.log("params: ", params);
    //   asyncTask(params).then(res => {
    //     console.log(res);
    //     const { code, data } = res;
    //     console.log("getAsyncTask- 异步任务", code, data);
    //     const { complete, success, errMsg } = data;
    //     if (code === 0 && complete) {
    //       pause();
    //       if (success) {
    //         ElMessage.success("导出成功");
    //         // loadingExport1.value = false;
    //         const { fileUrl, fileName } = data;
    //         downloadFileBySaveAs(fileUrl, fileName);
    //         exportLoading.value = false;
    //         loadingInstance.close();
    //       } else {
    //         ElMessage.error(errMsg);
    //         exportLoading.value = false;
    //         loadingInstance.close();
    //         // loadingExport1.value = false;
    //       }
    //     }
    //   });
    // }, 1000);
  } else {
    ElMessage.error("导出失败");
    // loadingInstance.close();
    // loadingExport1.value = false;
  }
  exportLoading.value = false;
};
// 关联订单/取消关联订单
const getButtonText = isPub => {
  return isPub === true ? "关联订单" : "取消关联订单";
};
const Freeze = async row => {
  console.log("🍭-----row-----", row);
  const isFreezing = !row.freeze;
  const confirmText = isFreezing
    ? "你确定要关联订单吗?"
    : "你确定要取消关联订单吗?";
  const confirmTitle = isFreezing ? "确认关联" : "确认取消关联";
  const successMessage = isFreezing ? "已关联" : "已取消关联";

  try {
    await ElMessageBox.confirm(confirmText, confirmTitle, {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning"
    });

    const params = {
      id: row.id,
      freeze: isFreezing
    };

    const { code, msg } = await isaccFreeze(params);

    if (code === 200) {
      ElMessage({
        message: successMessage,
        type: "success"
      });

      getTableList();
    } else {
      ElMessage.error(msg);
    }
  } catch (error) {
    // console.log("操作取消");
  }
};
const orderType = row => {
  let type = {
    WECHAT: "微信支付",
    ALPAY: "支付宝支付",
    PLATFORM: "平台",
    PUBLIC_ACCOUNT: "公账"
  };
  return type[row];
};

const formatStatisticValue = value => {
  if (!value || isNaN(Number(value))) return value;
  const num = Number(value);
  if (num % 1 === 0) {
    return num.toString();
  }
  return num.toFixed(2);
};

const defaultTime = [
  new Date(2024, 4, 1, 0, 0, 0),
  new Date(2024, 4, 1, 23, 59, 59)
];

const feeTypeMap = type => {
  const map = {
    CLASS_HOUR: "课时费",
    INSURANCE: "保险费",
    MATERIAL: "材料费",
    SERVICE: "服务费"
  };
  return map[type] || "";
};
</script>

<template>
  <div class="page-container">
    <div class="common">
      <div class="banner">
        <div class="detail-container">
          <el-statistic
            title="机构名称"
            :value="
              !organizationName || organizationName.trim() === ''
                ? '--'
                : organizationName
            "
          />

          <!-- <p>{{ organizationName }}</p> -->
          <!-- <p>总收入 {{ financialData?.totalIncome || "--" }}</p>
          <p>
            余额
            {{
              financialData?.totalIncome - financialData?.totalExpenditure ||
              "--"
            }}
          </p> -->
          <!-- <p>可提现金额 {{ financialData?.availableBalance || "--" }}</p> -->
          <el-statistic
            title="总收入"
            :value="financialData?.totalIncome || '0'"
            :formatter="formatStatisticValue"
          />
          <el-statistic
            title="余额"
            :value="financialData?.balance || '0'"
            :formatter="formatStatisticValue"
          />
          <!-- <el-statistic
            title="可提现金额"
            :value="financialData?.availableBalance || '0'"
          /> -->
        </div>
        <div class="function_container">
          <el-button type="primary" @click="goSet">银行账户信息</el-button>
          <el-button
            type="primary"
            :loading="exportLoading"
            @click="ProjectSubmission"
          >
            导出账单
          </el-button>
        </div>
      </div>
    </div>
    <div class="common">
      <div class="con_search">
        <el-form
          :model="form"
          style="width: 100%"
          label-width="70px"
          :inline="true"
        >
          <el-form-item label="支付时间">
            <el-date-picker
              v-model="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              :default-time="defaultTime"
              @change="timeChange"
            />
          </el-form-item>
          <!-- <el-form-item label="机构">
            <el-input v-model="form.name" placeholder="请输入" clearable />
          </el-form-item> -->
          <!-- <el-form-item label="机构">
            <el-input
              v-model="form.organizationName"
              placeholder="请输入"
              clearable
            />
          </el-form-item> -->
          <el-form-item label="类型">
            <el-select
              v-model="form.recordType"
              style="width: 120px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in courseTypeoptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="支付类型">
            <el-select
              v-model="form.channelType"
              style="width: 120px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in channelTypeoptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="费用类型">
            <el-select
              v-model="form.feeType"
              style="width: 120px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in feeTypeoptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label=" " style="float: right; margin-right: 0">
            <div class="flex">
              <el-button type="primary" @click="searchData"> 搜索 </el-button>
              <el-button @click="setData"> 重置 </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="common">
      <el-scrollbar :style="{ height: tableHeight }">
        <div class="con_table">
          <el-table
            :data="tableData"
            :max-height="tableHeight"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
          >
            <el-table-column prop="orderNo" label="订单号" min-width="130">
              <template #default="scope">
                <el-text
                  class="w-300px mb-2 btnse"
                  truncated
                  style="display: contents"
                  @click="edit(scope.row.ordersId)"
                >
                  {{ scope.row.orderNo || "--" }}
                </el-text>
              </template>
            </el-table-column>
            <el-table-column
              prop="coursePeriodName"
              label="课期名称"
              min-width="200"
            >
              <template #default="scope">
                <el-text
                  class="w-300px mb-2 btnse"
                  truncated
                  style="display: contents"
                  @click="getId(scope.row.coursePeriodId)"
                >
                  {{ scope.row.coursePeriodName || "--" }}
                </el-text>
              </template>
            </el-table-column>
            <el-table-column
              width="180px"
              prop="transactionTime"
              label="交易时间"
            >
              <template #default="scope">
                <div>
                  {{
                    formatTime(
                      scope.row?.transactionTime,
                      "YYYY-MM-DD HH:mm"
                    ) || "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="recordType"
              label="类型"
              align="left"
              width="100"
            >
              <template #default="scope">
                <div>
                  {{
                    scope.row.recordType === "INCOME" ? "收入" : "支出" || "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="channelType"
              label="支付渠道"
              align="left"
              width="120"
            >
              <template #default="scope">
                <div>
                  {{ orderType(scope.row.channelType) || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="feeType"
              label="费用类型"
              align="left"
              width="100"
            >
              <template #default="scope">
                <div>
                  {{ feeTypeMap(scope.row.feeType) || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="price" label="金额" align="left" width="100">
              <template #default="scope">
                <div>
                  {{
                    scope.row.price !== null && scope.row.price !== undefined
                      ? `￥${scope.row.price}`
                      : "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="originalPrice"
              label="原价"
              align="left"
              width="100"
            >
              <template #default="scope">
                <div>
                  {{
                    scope.row.originalPrice !== null &&
                    scope.row.originalPrice !== undefined
                      ? `￥${scope.row.originalPrice}`
                      : "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="remarks"
              label="备注"
              align="left"
              min-width="160"
            >
              <template #default="scope">
                <div>
                  {{ scope.row.remarks || "--" }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-scrollbar>

      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 89.1vh;
  overflow: hidden;
}

.scrollbar {
  background-color: #fff;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
}

:deep(.el-table .cell) {
  padding: 0;
}

.common {
  width: 100%;
  background-color: #fff;
  margin-bottom: 20px;
  padding: 20px 20px 2px 20px;

  // 最后一个common容器特殊处理，用于包含表格和分页
  &:last-child {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-bottom: 0;
    overflow: hidden;
  }

  .banner {
    width: 100%;
    background: #ffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 20px;
    .detail-container {
      display: flex;
      justify-content: space-between;
      min-width: 50vw;
    }
    .function_container {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-end;
      gap: 1.5vw;
    }
  }

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    .flex {
      float: right;
      gap: 1.5vw;
    }
  }

  .con_table {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fff;
    .button {
      display: flex;
      justify-content: left;
      // .btnse {
      //   color: #409eff;
      //   cursor: pointer;
      // }
    }
    .btnse {
      cursor: pointer;
      &:hover {
        color: #409eff;
      }
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    background: #fff;
    width: 100%;
    padding: 15px 20px;

    :deep(.el-pagination) {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-end;
      row-gap: 10px;

      .el-pagination__sizes,
      .el-pagination__jump {
        margin-bottom: 0;
      }
    }
  }
}
</style>
