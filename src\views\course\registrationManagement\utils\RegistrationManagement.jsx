import { ref, computed, nextTick, watch } from "vue";
import { useRouter } from "vue-router";
import { formatTime } from "@/utils/index";
import { getRegistrationManagementList, exportStudentList } from "@/api/course";
import { requestTo } from "@/utils/http/tool";
import { removeEmptyValues } from "@iceywu/utils";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { getAsyncTask } from "@/utils/common";

export default function useEnrollManage() {
  const router = useRouter();

  // 顶部查询
  const form = ref({
    timeRange: "",
    periodName: "",
    courseName: "",
    buyType: ""
  });

  // tab
  const tabs = ref([
    { label: "未开课", value: 0 },
    { label: "已完课", value: 1 }
  ]);
  const activeTab = ref(0);

  // 列表/分页
  const tableData = ref([]);
  const loading = ref(false);
  const pagination = ref({
    page: 1,
    size: 15,
    total: 0
  });

  // 计算表格高度（与订单管理一致）
  const tableHeight = ref("calc(100vh - 330px)");
  const calcHeight = async () => {
    await nextTick();
    const searchForm = document.querySelector(".con_search");
    if (searchForm) {
      tableHeight.value = `calc(100vh - 247px - ${searchForm.offsetHeight}px)`;
    }
  };
  calcHeight();
  window.addEventListener("resize", calcHeight);

  const buyTypeOptions = ref([
    { value: "", label: "全部" },
    { value: "ORDINARY", label: "普通单" },
    { value: "PRIVATE_DOMAIN_GROUP_ORDER", label: "团购单" }
  ]);

  // 获取列表数据
  const getList = async () => {
    loading.value = true;
    try {
      const params = {
        state: activeTab.value,
        page: pagination.value.page - 1, // API要求从0开始
        size: pagination.value.size,
        courseName: form.value.courseName || undefined,
        coursePeriodName: form.value.coursePeriodName || undefined,
        buyType: form.value.buyType || undefined
      };

      // 处理时间范围
      if (form.value.timeRange && form.value.timeRange.length === 2) {
        params.startTime = new Date(form.value.timeRange[0]).getTime();
        params.endTime = new Date(form.value.timeRange[1]).getTime();
      }

      const [err, res] = await requestTo(
        getRegistrationManagementList(removeEmptyValues(params))
      );

      if (res.content) {
        tableData.value = res.content || [];
        pagination.value.total = res.totalElements || 0;
      } else {
        console.error("获取列表失败:", err);
        tableData.value = [];
        pagination.value.total = 0;
      }
    } catch (error) {
      console.error("获取列表异常:", error);
      tableData.value = [];
      pagination.value.total = 0;
    } finally {
      loading.value = false;
    }
  };

  // 事件
  const onSearch = () => {
    pagination.value.page = 1;
    getList();
  };

  const onReset = () => {
    form.value = {
      timeRange: "",
      periodName: "",
      courseName: "",
      buyType: ""
    };
    pagination.value.page = 1;
    getList();
  };

  // 导出相关状态
  const exportLoading = ref(false);

  // 文件下载函数
  const downloadFileBySaveAs = (url, fileName) => {
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName || "学生名单.xlsx";
    a.click();
  };

  // 执行导出学生名单
  const exportStudentListData = async row => {
    exportLoading.value = true;

    // 创建全屏loading
    const loadingInstance = ElLoading.service({
      lock: true,
      text: "导出中...",
      background: "rgba(0, 0, 0, 0.7)"
    });

    try {
      const params = {
        coursePeriodId: row.coursePeriodId
      };

      const { code, data, msg } = await exportStudentList(params);

      if (code === 200 && data?.id) {
        // 等待异步任务完成
        const task = await getAsyncTask(data.id);

        if (task.data.complete && task.data.success) {
          let resData = {};
          if (task.data.res) {
            resData = JSON.parse(task.data.res);

            // 下载文件
            downloadFileBySaveAs(resData.url, resData.fileName);
            ElMessage.success("导出成功");
          }
        } else {
          ElMessage.error(task.data.errMsg || "导出失败");
        }
      } else {
        ElMessage.error(msg || "导出失败");
      }
    } catch (error) {
      console.error("导出异常:", error);
      ElMessage.error("导出失败");
    } finally {
      exportLoading.value = false;
      // 关闭loading
      loadingInstance.close();
    }
  };

  // 导出单行学生名单
  const onExportRow = async row => {
    if (exportLoading.value) return;

    ElMessageBox.confirm(
      `确认导出"${row.coursePeriodName}"的学生名单?`,
      "导出确认",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        draggable: true
      }
    )
      .then(() => {
        exportStudentListData(row);
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "取消导出"
        });
      });
  };

  const onTabChange = () => {
    pagination.value.page = 1;
    getList();
  };

  const onPageChange = p => {
    pagination.value.page = p;
    getList();
  };

  const onSizeChange = s => {
    pagination.value.size = s;
    pagination.value.page = 1;
    getList();
  };

  const viewPeriod = row => {
    router.push({
      path: "/course/signUpIndex/CourseDetails",
      query: { periodId: row.coursePeriodId, text: "course" }
    });
  };

  const viewCourse = row => {
    router.push({
      path: "/course/courseDetails",
      query: { id: row.coursePeriodId }
    });
  };

  // PlusSearch 配置
  const searchColumns = [
    {
      label: "课程名称",
      prop: "courseName",
      valueType: "input",
      fieldProps: {
        placeholder: "请输入课程名称",
        clearable: true,
        style: { width: "200px" }
      }
    },
    {
      label: "课期名称",
      prop: "coursePeriodName",
      valueType: "input",
      fieldProps: {
        placeholder: "请输入课期名称",
        clearable: true,
        style: { width: "200px" }
      }
    },
    {
      label: "时间范围",
      prop: "timeRange",
      valueType: "date-picker",
      fieldProps: {
        type: "daterange",
        rangeSeparator: "-",
        startPlaceholder: "开始日期",
        endPlaceholder: "结束日期",
        clearable: true,
        style: { width: "210px" }
      }
    },
    {
      label: "购买类型",
      prop: "buyType",
      valueType: "select",
      options: buyTypeOptions,
      fieldProps: {
        placeholder: "全部",
        clearable: true,
        style: { width: "200px" }
      }
    }
  ];

  // PlusTable 列配置
  const tableColumns = ref([
    {
      prop: "coursePeriodName",
      label: "课期名",
      minWidth: 180,
      align: "center",
      fixed: true,
      render: (value, record) => {
        const showTooltip = value.length > 20;
        return (
          <el-Tooltip content={value} placement="top" disabled={!showTooltip}>
            <div class="tooltip-cell">
              {value.length > 20 ? `${value.slice(0, 20)}...` : value}
            </div>
          </el-Tooltip>
        );
      }
    },
    {
      prop: "coursePeriodTermNumber",
      label: "期号",
      align: "center"
      // Width: 30
    },
    {
      prop: "coursePeriodOpenTime",
      label: "开课时间",
      minWidth: 100,
      render: (cellValue, row) => {
        return formatTime(row.row.coursePeriodOpenTime, "YYYY-MM-DD HH:mm");
      }
    },
    {
      prop: "signUpNumber",
      label: "报名人数",
      // Width: 80,
      render: (cellValue, row) => {
        if (row.row.minPeopleNumber === null) {
          row.row.minPeopleNumber = 0;
        }
        const isOverLimit = row.row.signUpNumber > row.row.minPeopleNumber;
        return (
          <span
            style={{
              color: isOverLimit ? "rgb(84, 157, 253)" : "rgb(248, 123, 123)"
            }}
          >
            {row.row.signUpNumber}
          </span>
        );
      }
    },
    {
      prop: "buyMaterialNumber",
      label: "购买材料费及人数"
      // Width: 20
    },
    {
      prop: "buyType",
      label: "购买类型",
      //  Width: 120,
      render: (cellValue, row) => {
        const option = buyTypeOptions.value.find(
          opt => opt.value === row.row.buyType
        );
        return option ? option.label : row.row.buyType;
      }
    },
    {
      prop: "minPeopleNumber",
      label: "人数下限",
      // Width: 10,
      render: (cellValue, row) => {
        return row.row?.minPeopleNumber === null
? (
          <span>--</span>
        )
: (
          <span>{row.row.minPeopleNumber}</span>
        );
      }
    },
    {
      prop: "maxPeopleNumber",
      label: "人数上限",
      // Width: 80,
      render: (cellValue, row) => {
        return row.row?.maxPeopleNumber === null
? (
          <span>--</span>
        )
: (
          <span>{row.row.maxPeopleNumber}</span>
        );
      }
    }
  ]);

  // 操作按钮配置
  const buttons = ref([
    {
      text: "课期详情",
      code: "viewPeriod",
      props: {
        type: "text",
        size: "mini"
      },
      show: () => true,
      onClick: params => {
        viewPeriod(params.row);
      }
    },
    {
      text: "导出学生名单",
      code: "exportStudent",
      props: {
        type: "text",
        size: "mini"
      },
      show: () => true,
      onClick: params => {
        onExportRow(params.row);
      }
    }
  ]);

  // 初始化加载数据
  const initData = () => {
    getList();
  };

  return {
    // state
    tableHeight,
    loading,
    tabs,
    activeTab,
    form,
    tableData,
    pagination,
    searchColumns,
    tableColumns,
    buttons,
    exportLoading,

    // events
    onSearch,
    onReset,
    // onExportAll,
    onExportRow,
    onTabChange,
    onPageChange,
    onSizeChange,
    viewPeriod,
    viewCourse,
    initData
  };
}
